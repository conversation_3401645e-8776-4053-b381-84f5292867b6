name: Deploy to HuggingFace Spaces

on:
  push:
    branches:
      - dev
      - main
  workflow_dispatch:

jobs:
  check-secret:
    runs-on: ubuntu-latest
    outputs:
      token-set: ${{ steps.check-key.outputs.defined }}
    steps:
      - id: check-key
        env:
          HF_TOKEN: ${{ secrets.HF_TOKEN }}
        if: "${{ env.HF_TOKEN != '' }}"
        run: echo "defined=true" >> $GITHUB_OUTPUT

  deploy:
    runs-on: ubuntu-latest
    needs: [check-secret]
    if: needs.check-secret.outputs.token-set == 'true'
    env:
      HF_TOKEN: ${{ secrets.HF_TOKEN }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          lfs: true

      - name: Remove git history
        run: rm -rf .git

      - name: Prepend YAML front matter to README.md
        run: |
          echo "---" > temp_readme.md
          echo "title: Open WebUI" >> temp_readme.md
          echo "emoji: 🐳" >> temp_readme.md
          echo "colorFrom: purple" >> temp_readme.md
          echo "colorTo: gray" >> temp_readme.md
          echo "sdk: docker" >> temp_readme.md
          echo "app_port: 8080" >> temp_readme.md
          echo "---" >> temp_readme.md
          cat README.md >> temp_readme.md
          mv temp_readme.md README.md

      - name: Configure git
        run: |
          git config --global user.email "41898282+github-actions[bot]@users.noreply.github.com"
          git config --global user.name "github-actions[bot]"
      - name: Set up Git and push to Space
        run: |
          git init --initial-branch=main
          git lfs install
          git lfs track "*.ttf"
          git lfs track "*.jpg"
          rm demo.gif
          git add .
          git commit -m "GitHub deploy: ${{ github.sha }}"
          git push --force https://open-webui:${HF_TOKEN}@huggingface.co/spaces/open-webui/open-webui main

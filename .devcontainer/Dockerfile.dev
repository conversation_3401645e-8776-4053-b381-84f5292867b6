# Use the official Python devcontainer base image
ARG VARIANT=3.11-bookworm
FROM mcr.microsoft.com/devcontainers/python:${VARIANT}

# Install system dependencies in a single layer with cleanup
RUN apt-get update && apt-get install -y --no-install-recommends \
    git \
    build-essential \
    pandoc \
    gcc \
    python3-dev \
    netcat-openbsd \
    curl \
    jq \
    ffmpeg \
    libsm6 \
    libxext6 \
    socat \
    sqlite3 \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Set up non-root user
ARG USERNAME=vscode
ARG USER_UID=1000
ARG USER_GID=$USER_UID

# Create cache directories and set permissions
RUN mkdir -p /home/<USER>/.npm /home/<USER>/.cache/pip /home/<USER>/.cache/uv \
    && chown -R $USERNAME:$USERNAME /home/<USER>/.npm /home/<USER>/.cache

# Pre-install global tools to avoid reinstalling
# Install uv globally and add to system PATH
RUN pip3 install --no-cache-dir uv && \
    ln -s /usr/local/bin/uv /usr/bin/uv

# Set environment variables for caching and optimization
ENV NPM_CONFIG_CACHE=/home/<USER>/.npm
ENV PIP_CACHE_DIR=/home/<USER>/.cache/pip
ENV UV_CACHE_DIR=/home/<USER>/.cache/uv
ENV UV_LINK_MODE=copy

# Switch to non-root user
USER $USERNAME

# Set working directory
WORKDIR /workspaces/open-webui

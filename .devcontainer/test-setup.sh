#!/bin/bash

echo "🔧 Testing devcontainer setup..."

# Check if devcontainer CLI is available
if ! command -v devcontainer &> /dev/null; then
    echo "❌ devcontainer CLI not found. Install with: npm install -g @devcontainers/cli"
    exit 1
fi

echo "✅ devcontainer CLI found: $(which devcontainer)"

# Check if port forwarder is available
if [ ! -f ".devcontainer/devcontainer-cli-port-forwarder/forwarder.py" ]; then
    echo "❌ Port forwarder not found"
    exit 1
fi

echo "✅ Port forwarder found: .devcontainer/devcontainer-cli-port-forwarder/forwarder.py"

# Check if devcontainer.json has the required configuration
if grep -q "initializeCommand" .devcontainer/devcontainer.json; then
    echo "✅ initializeCommand found in devcontainer.json"
else
    echo "❌ initializeCommand not found in devcontainer.json"
    exit 1
fi

if grep -q "forwardPorts" .devcontainer/devcontainer.json; then
    echo "✅ forwardPorts found in devcontainer.json"
    PORTS=$(grep -A 1 "forwardPorts" .devcontainer/devcontainer.json | grep -o '\[.*\]')
    echo "📋 Ports to forward: $PORTS"
else
    echo "❌ forwardPorts not found in devcontainer.json"
    exit 1
fi

echo ""
echo "🎉 Setup verification complete!"
echo ""
echo "📋 Next steps:"
echo "  1. Start the devcontainer: devcontainer up --workspace-folder ."
echo "  2. The port forwarder will automatically start and forward ports [5173, 8080]"
echo "  3. Access your services at:"
echo "     • Frontend: http://localhost:5173"
echo "     • Backend: http://localhost:8080"
echo ""
echo "🔍 To check port forwarder logs:"
echo "  ps aux | grep forwarder.py"
echo ""
echo "🛑 To stop the devcontainer:"
echo "  devcontainer down --workspace-folder ."

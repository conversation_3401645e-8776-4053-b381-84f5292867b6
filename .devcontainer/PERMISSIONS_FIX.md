# Devcontainer Permissions Fix

## Problem Identified

VS Code was failing to rebuild the devcontainer due to npm permissions issues:

```
npm error code EACCES
npm error syscall mkdir
npm error path /workspaces/open-webui/node_modules/@aashutoshrathi
npm error errno -13
npm error Error: EACCES: permission denied, mkdir '/workspaces/open-webui/node_modules/@aashutoshrathi'
```

## Root Cause

The issue was caused by:
1. **Volume mounts** being owned by root instead of the `vscode` user
2. **Cache directories** pointing to `/tmp` and `/root` instead of user-accessible locations
3. **Permission mismatch** between the container user (`vscode`) and volume ownership

## Fixes Applied

### 1. Updated Volume Mounts
Changed cache directories to use the `vscode` user's home directory:

```json
"mounts": [
    "source=openwebui-node-modules,target=${containerWorkspaceFolder}/node_modules,type=volume",
    "source=openwebui-pip-cache,target=/home/<USER>/.cache/pip,type=volume",
    "source=openwebui-uv-cache,target=/home/<USER>/.cache/uv,type=volume",
    "source=openwebui-npm-cache,target=/home/<USER>/.npm,type=volume",
    // ... other mounts
]
```

### 2. Updated Environment Variables
Changed cache environment variables to point to user directories:

```json
"containerEnv": {
    "NPM_CONFIG_CACHE": "/home/<USER>/.npm",
    "PIP_CACHE_DIR": "/home/<USER>/.cache/pip",
    "UV_CACHE_DIR": "/home/<USER>/.cache/uv"
}
```

### 3. Fixed postCreateCommand
Added permission fix at the start of the setup process:

```bash
sudo chown -R vscode:vscode /workspaces/open-webui/node_modules /home/<USER>/.npm /home/<USER>/.cache || true
```

### 4. Updated Dockerfile
Modified the Dockerfile to:
- Create cache directories in the user's home directory
- Set proper ownership during build
- Use user-accessible paths for environment variables

## Benefits of the Fix

### ✅ Immediate Benefits
- **Eliminates permission errors** during npm install
- **Proper cache ownership** for all package managers
- **Consistent user experience** across rebuilds

### ✅ Long-term Benefits
- **Better security** - no root-owned files in user workspace
- **Faster rebuilds** - proper caching with persistent volumes
- **Predictable behavior** - consistent permissions across container rebuilds

## How to Test the Fix

1. **Rebuild the container** in VS Code:
   - `Ctrl+Shift+P` → "Dev Containers: Rebuild Container"

2. **Verify the build succeeds** without permission errors

3. **Check cache directories** are properly owned:
   ```bash
   ls -la /home/<USER>/.npm
   ls -la /home/<USER>/.cache/
   ls -la /workspaces/open-webui/node_modules
   ```

## Files Modified

- `.devcontainer/devcontainer.json` - Updated mounts, environment variables, and postCreateCommand
- `.devcontainer/Dockerfile.dev` - Updated to use user home directory for caches

## Rollback Instructions

If issues persist, you can rollback to the previous configuration:

```bash
cp .devcontainer/backups/devcontainer.json.current-backup .devcontainer/devcontainer.json
```

## Expected Behavior

After this fix:
- Container rebuild should complete without errors
- npm commands should work without permission issues
- All caches should persist between rebuilds
- Development environment should be ready for use

The permissions fix ensures that the `vscode` user has proper ownership of all cache directories and the node_modules volume, eliminating the EACCES errors that were preventing successful container builds.

# Terminal and Service Troubleshooting Guide

## Issues Identified

1. **VS Code Terminal Hanging**: You can't run commands in the VS Code terminal
2. **Services Not Accessible**: localhost:5173 and localhost:8080 not working
3. **Frontend Service Issues**: npm run dev may be hanging on pyodide:fetch

## Immediate Solutions

### Fix 1: Reset VS Code Connection

1. **Close VS Code completely**
2. **Restart VS Code**
3. **Reopen the folder in the devcontainer**
4. **Try opening a new terminal**

### Fix 2: Use Docker Exec for Testing

If VS Code terminal is hanging, you can test services directly:

```bash
# From your host machine (outside VS Code)
docker exec -it a6e9e651cceee2e1b605981bd47872fafba5e41a4dda0cc649b4bd42b6f9d6b1 bash

# Then inside the container:
cd /workspaces/open-webui
npm run dev
```

### Fix 3: Alternative Service Start Commands

The standard `npm run dev` may hang due to pyodide:fetch. Try these alternatives:

#### Option A: Skip pyodide and start directly
```bash
# In container
cd /workspaces/open-webui
npx vite dev --host 0.0.0.0 --port 5173
```

#### Option B: Start backend first
```bash
# In container
cd /workspaces/open-webui/backend
python3 -m uvicorn open_webui.main:app --host 0.0.0.0 --port 8080 --reload
```

#### Option C: Use the dev script directly
```bash
# In container
cd /workspaces/open-webui/backend
bash dev.sh
```

## Current Service Status

Based on our testing:
- ✅ **Container is running**: a6e9e651cceee2e1b605981bd47872fafba5e41a4dda0cc649b4bd42b6f9d6b1
- ✅ **VS Code server is running**: Multiple vscode processes active
- ✅ **Frontend partially started**: Node process running on port 5173
- ❌ **Frontend not fully accessible**: Returns 404 (likely still building)
- ❌ **Backend not started**: No service on port 8080

## Diagnostic Commands

### Check Container Status
```bash
docker ps
docker exec -it CONTAINER_ID ps aux
```

### Check Port Status
```bash
docker exec -it CONTAINER_ID netstat -tlpn | grep -E "(5173|8080)"
```

### Check Service Logs
```bash
docker exec -it CONTAINER_ID bash -c "cd /workspaces/open-webui && npm run dev 2>&1"
```

### Test Port Forwarding
```bash
# From host
curl -s -o /dev/null -w "%{http_code}" http://localhost:5173
curl -s -o /dev/null -w "%{http_code}" http://localhost:8080
```

## VS Code Terminal Issues

### Common Causes:
1. **VS Code Remote Connection Problems**
2. **devcontainer.json Configuration Issues**
3. **Container Resource Constraints**
4. **Port Forwarding Conflicts**

### Solutions:

#### Solution 1: Reload VS Code Window
- `Ctrl+Shift+P` → "Developer: Reload Window"

#### Solution 2: Restart Dev Container
- `Ctrl+Shift+P` → "Dev Containers: Rebuild Container"

#### Solution 3: Check Terminal Settings
- `Ctrl+Shift+P` → "Terminal: Select Default Profile"
- Ensure it's set to `bash` or `zsh`

#### Solution 4: Manual Terminal Creation
- `Ctrl+Shift+P` → "Terminal: Create New Terminal"
- Or use: Terminal → New Terminal

## Service Startup Issues

### Frontend Issues:

#### Issue: `npm run dev` hangs on pyodide:fetch
```bash
# Alternative: Start Vite directly
npx vite dev --host 0.0.0.0 --port 5173

# Or build first, then start
npm run build
npm run preview
```

#### Issue: Port 5173 not accessible
```bash
# Check if service is running
docker exec -it CONTAINER_ID ps aux | grep vite

# Check if port is listening
docker exec -it CONTAINER_ID netstat -tlpn | grep 5173

# Test from inside container
docker exec -it CONTAINER_ID curl http://localhost:5173
```

### Backend Issues:

#### Issue: Backend won't start
```bash
# Check dev.sh script
cat backend/dev.sh

# Start manually
cd backend
python3 -m uvicorn open_webui.main:app --host 0.0.0.0 --port 8080 --reload
```

#### Issue: Python import errors
```bash
# Check Python environment
python3 --version
python3 -m pip list | grep -i fastapi
python3 -m pip list | grep -i uvicorn

# Check if main module exists
ls -la backend/open_webui/main.py
```

## Quick Fix Script

Create this script to start services without VS Code terminal:

```bash
#!/bin/bash
# save as quick-start.sh

echo "Starting services via Docker exec..."

# Start frontend in background
docker exec -d a6e9e651cceee2e1b605981bd47872fafba5e41a4dda0cc649b4bd42b6f9d6b1 bash -c "cd /workspaces/open-webui && npx vite dev --host 0.0.0.0 --port 5173"

# Start backend in background
docker exec -d a6e9e651cceee2e1b605981bd47872fafba5e41a4dda0cc649b4bd42b6f9d6b1 bash -c "cd /workspaces/open-webui/backend && python3 -m uvicorn open_webui.main:app --host 0.0.0.0 --port 8080 --reload"

echo "Services started. Check:"
echo "Frontend: http://localhost:5173"
echo "Backend: http://localhost:8080"
```

## Alternative: Use Docker Compose

If VS Code continues to have issues, you can use docker-compose:

```bash
# From host machine
cd /path/to/open-webui
docker-compose up -d
```

## Recovery Steps

1. **Immediate**: Use docker exec to test services
2. **Short term**: Restart VS Code and devcontainer
3. **Long term**: If issues persist, consider using docker-compose instead

## Success Indicators

✅ **VS Code Terminal Working**: You can run commands and see output
✅ **Frontend Accessible**: http://localhost:5173 shows the app
✅ **Backend Accessible**: http://localhost:8080 shows API documentation
✅ **Both Services Running**: Full application functionality

## Getting Help

If issues persist:
1. Check VS Code Remote-Containers extension logs
2. Check Docker logs: `docker logs CONTAINER_ID`
3. Verify port forwarding in VS Code: View → Command Palette → "Ports: Focus on Ports View"

{
	"name": "Open WebUI Development Environment",
	"build": {
		"dockerfile": "./Dockerfile.dev",
		"context": "..",
		"args": {
			"VARIANT": "3.11-bookworm"
		}
	},
	"features": {
		"ghcr.io/devcontainers/features/common-utils:2": {
			"installZsh": true,
			"configureZshAsDefaultShell": true,
			"installOhMyZsh": true,
			"upgradePackages": false
		},
		"ghcr.io/devcontainers/features/node:1": {
			"nodeGypDependencies": true,
			"version": "lts",
			"nvmVersion": "latest"
		},
		"ghcr.io/devcontainers/features/python:1": {
			"version": "3.11",
			"installTools": true
		}
	},

	// Use volume mounts for better performance and caching
	"mounts": [
		// Cache npm packages - use home directory to avoid permissions issues
		"source=openwebui-node-modules,target=${containerWorkspaceFolder}/node_modules,type=volume",
		// Cache pip packages
		"source=openwebui-pip-cache,target=/home/<USER>/.cache/pip,type=volume",
		// Cache uv packages
		"source=openwebui-uv-cache,target=/home/<USER>/.cache/uv,type=volume",
		// Cache npm packages in user directory
		"source=openwebui-npm-cache,target=/home/<USER>/.npm,type=volume",
		// Cache hugging face models
		"source=openwebui-hf-cache,target=${containerWorkspaceFolder}/backend/data/cache,type=volume",
		// Cache apt packages
		"source=openwebui-apt-cache,target=/var/cache/apt,type=volume",
		"source=openwebui-apt-lib,target=/var/lib/apt,type=volume"
	],

	// Optimized postCreateCommand - uv is already installed in Dockerfile
	"postCreateCommand": [
		"bash",
		"-c",
		"echo 'Setting up development environment...' && sudo chown -R vscode:vscode /workspaces/open-webui/node_modules /home/<USER>/.npm /home/<USER>/.cache || true && npm ci --prefer-offline --no-audit --no-fund && echo 'Frontend dependencies installed' && cd backend && uv pip install --system -r requirements.txt && echo 'Backend dependencies installed' && mkdir -p data/{uploads,cache,logs} && echo 'Development environment ready!'"
	],

	// Fast startup message
	"postStartCommand": "echo '🚀 Development environment ready!' && echo 'Frontend: npm run dev | Backend: cd backend && bash dev.sh'",

	// Forward ports
	"forwardPorts": [5173, 8080],

	// Port attributes for better handling
	"portsAttributes": {
		"5173": {
			"label": "Frontend (Vite)",
			"onAutoForward": "ignore"
		},
		"8080": {
			"label": "Backend (FastAPI)",
			"onAutoForward": "ignore"
		}
	},

	// Environment variables
	"containerEnv": {
		"NODE_ENV": "development",
		"ENV": "dev",
		"PYTHONPATH": "${containerWorkspaceFolder}/backend",
		// Ramalama CLI configuration - OpenAI compatible endpoint
		"OPENAI_API_BASE_URL": "http://**************:11434/v1",
		"OPENAI_API_KEY": "dummy-key-for-local-api",
		// Multiple API URL configurations to ensure proper routing
		"OPENAI_API_BASE_URLS": "http://**************:11434/v1",
		"OPENAI_API_KEYS": "dummy-key-for-local-api",
		// Disable OpenAI API to force custom endpoint usage
		"ENABLE_OPENAI_API": "true",
		// Core application settings
		"WEBUI_SECRET_KEY": "dev-secret-key-change-in-production",
		"PORT": "8080",
		"SCARF_NO_ANALYTICS": "true",
		"DO_NOT_TRACK": "true",
		"ANONYMIZED_TELEMETRY": "false",
		// Audio/Speech configuration
		"WHISPER_MODEL": "base",
		"WHISPER_MODEL_DIR": "${containerWorkspaceFolder}/backend/data/cache/whisper/models",
		"SENTENCE_TRANSFORMERS_HOME": "${containerWorkspaceFolder}/backend/data/cache/embedding/models",
		"TIKTOKEN_CACHE_DIR": "${containerWorkspaceFolder}/backend/data/cache/tiktoken",
		"HF_HOME": "${containerWorkspaceFolder}/backend/data/cache/embedding/models",
		// Optimize package managers
		"NPM_CONFIG_CACHE": "/home/<USER>/.npm",
		"PIP_CACHE_DIR": "/home/<USER>/.cache/pip",
		"UV_CACHE_DIR": "/home/<USER>/.cache/uv"
	},

	// Security and performance
	"remoteUser": "vscode",
	"updateRemoteUserUID": true,
	"shutdownAction": "stopContainer",
	"init": true,
	"privileged": false,
	"capAdd": [],
	"securityOpt": [],
	"workspaceFolder": "/workspaces/open-webui",
	"overrideCommand": true,
	"waitFor": "postCreateCommand",

	// VS Code customizations
	"customizations": {
		"vscode": {
			"extensions": [
				"ms-python.python",
				"ms-python.pylint",
				"ms-python.black-formatter",
				"njpwerner.autodocstring",
				"ms-python.flake8",
				"ms-python.mypy-type-checker",
				"dbaeumer.vscode-eslint",
				"esbenp.prettier-vscode",
				"ms-vscode.vscode-typescript-next",
				"svelte.svelte-vscode",
				"streetsidesoftware.code-spell-checker",
				"ms-vscode.vscode-json",
				"redhat.vscode-yaml",
				"ms-vscode-remote.remote-containers",
				"ms-vscode.remote-repositories",
				"eamodio.gitlens",
				"github.vscode-pull-request-github",
				"mtxr.sqltools",
				"mtxr.sqltools-driver-pg",
				"ms-toolsai.jupyter",
				"ms-toolsai.vscode-jupyter-cell-tags",
				"ms-toolsai.vscode-jupyter-slideshow",
				"humao.rest-client",
				"ms-azuretools.vscode-docker",
				"yzhang.markdown-all-in-one",
				"shd101wyy.markdown-preview-enhanced",
				"pkief.material-icon-theme",
				"github.github-vscode-theme",
				"Augment.vscode-augment"
			],
			"settings": {
				// General editor settings
				"editor.formatOnSave": false,
				"editor.codeActionsOnSave": {
					"source.organizeImports": "never",
					"source.fixAll.eslint": "never"
				},
				"editor.rulers": [88, 120],
				"editor.tabSize": 4,
				"editor.insertSpaces": false,
				"files.trimTrailingWhitespace": true,
				"files.insertFinalNewline": true,
				"files.trimFinalNewlines": true,

				// Python specific settings
				"[python]": {
					"editor.defaultFormatter": null,
					"editor.insertSpaces": false,
					"editor.tabSize": 4,
					"editor.detectIndentation": false
				},
				"python.defaultInterpreterPath": "/usr/local/bin/python",
				"python.testing.pytestEnabled": true,
				"python.testing.autoTestDiscoverOnSaveEnabled": true,

				// JavaScript/TypeScript settings
				"[javascript]": {
					"editor.defaultFormatter": null,
					"editor.tabSize": 2,
					"editor.insertSpaces": true
				},
				"[typescript]": {
					"editor.defaultFormatter": "vscode.typescript-language-features",
					"editor.tabSize": 2,
					"editor.insertSpaces": true
				},

				// Svelte settings
				"[svelte]": {
					"editor.defaultFormatter": null,
					"editor.tabSize": 2,
					"editor.insertSpaces": true
				},

				// JSON/YAML settings
				"[json]": {
					"editor.defaultFormatter": "vscode.json-language-features",
					"editor.tabSize": 2,
					"editor.insertSpaces": true
				},
				"[yaml]": {
					"editor.defaultFormatter": null,
					"editor.tabSize": 2,
					"editor.insertSpaces": true
				},

				// Markdown settings
				"[markdown]": {
					"editor.wordWrap": "on",
					"editor.quickSuggestions": {
						"comments": "off",
						"strings": "off",
						"other": "off"
					}
				},

				// Terminal settings
				"terminal.integrated.defaultProfile.linux": "zsh",
				"terminal.integrated.profiles.linux": {
					"zsh": {
						"path": "/bin/zsh"
					}
				},

				// Git settings
				"git.autofetch": true,
				"git.enableSmartCommit": true,
				"git.confirmSync": false,

				// File associations
				"files.associations": {
					"*.env*": "properties",
					"Dockerfile*": "dockerfile",
					"docker-compose*.yml": "dockercompose",
					"docker-compose*.yaml": "dockercompose"
				},

				// Workspace settings
				"workbench.startupEditor": "newUntitledFile",

				// Explorer settings
				"explorer.confirmDelete": false,
				"explorer.confirmDragAndDrop": false,

				// Search settings
				"search.exclude": {
					"**/node_modules": true,
					"**/bower_components": true,
					"**/.git": true,
					"**/.svn": true,
					"**/.hg": true,
					"**/CVS": true,
					"**/.DS_Store": true,
					"**/tmp": true,
					"**/coverage": true,
					"**/dist": true,
					"**/build": true,
					"**/__pycache__": true,
					"**/.pytest_cache": true,
					"**/*.pyc": true
				}
			}
		}
	}
}

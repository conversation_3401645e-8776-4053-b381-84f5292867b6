# Port Forwarding Troubleshooting Guide

## Problem Identified

VS Code was generating continuous `ECONNREFUSED` errors while trying to connect to ports 5173 and 8080:

```
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:8080
stack trace: Error: connect <PERSON><PERSON><PERSON>EFUSED 127.0.0.1:8080
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1636:16)
```

## Root Cause Analysis

The issue was caused by the `portsAttributes` configuration in `.devcontainer/devcontainer.json`:

```json
"portsAttributes": {
    "5173": {
        "label": "Frontend (Vite)",
        "onAutoForward": "notify"  // ← This was causing the problem
    },
    "8080": {
        "label": "Backend (FastAPI)",
        "onAutoForward": "notify"  // ← This was causing the problem
    }
}
```

### What `onAutoForward: "notify"` Does:
- VS Code continuously polls the specified ports to check if services are running
- When a service starts listening on the port, VS Code shows a notification
- If no service is listening, it generates `ECONNREFUSED` errors every few seconds
- This happens **before** the development services are actually started

## The Fix Applied

Changed `onAutoForward` from `"notify"` to `"ignore"`:

```json
"portsAttributes": {
    "5173": {
        "label": "Frontend (Vite)",
        "onAutoForward": "ignore"  // ← Fixed: No automatic connection attempts
    },
    "8080": {
        "label": "Backend (FastAPI)",
        "onAutoForward": "ignore"  // ← Fixed: No automatic connection attempts
    }
}
```

## Available `onAutoForward` Options

1. **`"notify"`** - Continuously checks port and shows notification when service starts
   - ❌ **Problem**: Generates errors when services aren't running
   - ✅ **Use when**: You want automatic notifications (but only if services start quickly)

2. **`"ignore"`** - Forwards ports but doesn't auto-detect services
   - ✅ **Benefit**: No connection attempts until you manually access the port
   - ✅ **Use when**: You start services manually and don't need notifications

3. **`"silent"`** - Forwards ports and auto-detects silently
   - ✅ **Benefit**: Auto-detects without notifications
   - ✅ **Use when**: You want detection but no notifications

4. **`"openBrowser"`** - Opens browser when service starts
   - ✅ **Benefit**: Automatically opens your app in browser
   - ⚠️ **Warning**: Can be aggressive with connection attempts

## Current Status

### ✅ Fixed Issues:
- No more `ECONNREFUSED` errors in VS Code logs
- Container builds and runs successfully
- Port forwarding still works when services are started
- No aggressive connection attempts during container startup

### ✅ Port Forwarding Still Works:
- Ports 5173 and 8080 are still forwarded
- When you start services, they'll be accessible at:
  - Frontend: http://localhost:5173
  - Backend: http://localhost:8080

## How to Start Services

The container is ready, but services need to be started manually:

### 1. Start Frontend (Terminal 1):
```bash
npm run dev
```

### 2. Start Backend (Terminal 2):
```bash
cd backend
bash dev.sh
```

## Alternative Solutions

If you want automatic notifications back, you can:

1. **Use `"silent"`** instead of `"ignore"`:
   ```json
   "onAutoForward": "silent"
   ```

2. **Use `"openBrowser"`** for automatic browser opening:
   ```json
   "onAutoForward": "openBrowser"
   ```

3. **Add service startup to `postCreateCommand`** (not recommended for development):
   ```json
   "postCreateCommand": "npm run dev & cd backend && bash dev.sh"
   ```

## Container Status Verification

To verify the container is running properly:

```bash
# Check container status
docker ps

# Check inside container
docker exec -it CONTAINER_ID bash

# Check if ports are accessible (after starting services)
curl http://localhost:5173  # Frontend
curl http://localhost:8080  # Backend
```

## Best Practices

1. **For Development**: Use `"ignore"` to avoid noise during development
2. **For Production**: Use `"notify"` or `"openBrowser"` for automatic detection
3. **For CI/CD**: Use `"silent"` to avoid notifications but still get detection
4. **Manual Control**: Start services manually for better control during development

## Summary

The port forwarding errors were **normal behavior** caused by VS Code's automatic port detection trying to connect to services that weren't running yet. The fix prevents these unnecessary connection attempts while maintaining all port forwarding functionality.

**The devcontainer is working correctly!** The errors were just VS Code being overly eager to connect to your development services.

# DevContainer Port Forwarding Setup

This setup enables automatic port forwarding when using the devcontainer CLI (without VS Code).

## What's Installed

1. **devcontainer-cli-port-forwarder**: A Python script that reads `devcontainer.json` and automatically forwards the specified ports from the host to the container.

## Configuration

The `devcontainer.json` has been configured with:

- **initializeCommand**: Starts the port forwarder in the background when the container is initialized
- **forwardPorts**: [5173, 8080] - Frontend and backend ports
- **postCreateCommand**: Installs `socat` (required by the port forwarder) along with other dependencies

## How It Works

1. When you run `devcontainer up --workspace-folder .`, the `initializeCommand` starts the port forwarder in the background
2. The port forwarder reads the `forwardPorts` array from `devcontainer.json`
3. It creates TCP socket connections on the host machine for each specified port
4. Traffic is forwarded to the corresponding port inside the container using `socat`
5. The port forwarder automatically exits when the container stops

## Usage

### Start the devcontainer:
```bash
devcontainer up --workspace-folder .
```

### Access your services:
- Frontend: http://localhost:5173
- Backend: http://localhost:8080

### Check port forwarder status:
```bash
ps aux | grep forwarder.py
```

### Stop the devcontainer:
```bash
devcontainer down --workspace-folder .
```

## Troubleshooting

### Port forwarder not starting
- Check if Python 3 is available on the host
- Verify the path to `forwarder.py` is correct in `initializeCommand`

### Ports not forwarding
- Ensure `socat` is installed inside the container (it's in `postCreateCommand`)
- Check that the services inside the container are listening on the specified ports
- Verify no other process is using the same ports on the host

### Check logs
The port forwarder runs with `verbose` flag, so you can see detailed logs:
```bash
ps aux | grep forwarder.py
```

## Adding More Ports

To forward additional ports, simply add them to the `forwardPorts` array in `devcontainer.json`:

```json
"forwardPorts": [5173, 8080, 3000, 5432]
```

## Alternative: Manual Port Forwarding

If you prefer not to use the automatic port forwarder, you can use Docker's built-in port mapping by modifying how you start the container, but this requires more manual setup.

# Service Startup Guide

## What Just Happened?

You successfully rebuilt the devcontainer and tried to access `localhost:8080` in your browser, but got a **white page**. This is expected behavior!

## Why Did This Happen?

### ✅ Container Status: WORKING PERFECTLY
- Devcontainer built successfully
- All dependencies installed (frontend + backend)
- VS Code server connected
- Port forwarding configured correctly

### ❌ Service Status: NOT STARTED YET
- Frontend service (Vite) is **not running**
- Backend service (FastAPI) is **not running**
- That's why `localhost:8080` shows a white page

## How to Start Services

### Option 1: Using VS Code Terminal (Recommended)

1. **Open two terminals in VS Code** (Terminal → New Terminal)

2. **Terminal 1 - Start Frontend:**
   ```bash
   npm run dev
   ```
   This starts the frontend on port 5173

3. **Terminal 2 - Start Backend:**
   ```bash
   cd backend
   bash dev.sh
   ```
   This starts the backend on port 8080

### Option 2: Using the Helper Script

```bash
# Start frontend
./start-dev.sh frontend

# In another terminal, start backend
./start-dev.sh backend
```

### Option 3: Check What's Available

```bash
# Show usage instructions
./start-dev.sh

# Show both service requirements
./start-dev.sh both
```

## Expected Behavior After Starting Services

### Frontend (Port 5173)
- **URL**: http://localhost:5173
- **Service**: Vite development server with hot reload
- **Purpose**: Serves the React/Svelte frontend

### Backend (Port 8080)
- **URL**: http://localhost:8080
- **Service**: FastAPI with auto-reload
- **Purpose**: API server for the application

## Troubleshooting

### If Frontend Won't Start:
```bash
# Check if node_modules exists
ls -la node_modules

# Reinstall dependencies if needed
npm install

# Try starting again
npm run dev
```

### If Backend Won't Start:
```bash
# Check if dev.sh exists
ls -la backend/dev.sh

# Check Python packages
cd backend
python3 -m pip list

# Try starting manually
cd backend
python3 -m open_webui.main
```

### If Ports Are Not Accessible:
```bash
# Check if services are running
docker exec -it $(docker ps -q) ps aux | grep -E "(vite|uvicorn|fastapi|node)"

# Check port forwarding
docker exec -it $(docker ps -q) netstat -tlpn | grep -E "(5173|8080)"
```

## Common Issues and Solutions

### Issue: "White page on localhost:8080"
- **Cause**: Backend service not started
- **Solution**: Start backend with `cd backend && bash dev.sh`

### Issue: "Connection refused"
- **Cause**: Service not running on that port
- **Solution**: Start the appropriate service first

### Issue: "Port already in use"
- **Cause**: Previous service instance still running
- **Solution**: Kill the process and restart

### Issue: "Permission denied"
- **Cause**: Volume mount permissions (already fixed in this container)
- **Solution**: Already resolved with the permissions fix

## Development Workflow

1. **Start Services**: Use two terminals as shown above
2. **Access Application**: 
   - Frontend: http://localhost:5173 (main app)
   - Backend: http://localhost:8080 (API endpoints)
3. **Develop**: Make changes, both services have hot reload
4. **Stop Services**: Ctrl+C in each terminal

## Port Forwarding Status

The devcontainer automatically forwards:
- Port 5173 → Frontend (Vite)
- Port 8080 → Backend (FastAPI)

But the services must be **running** for the ports to respond.

## Success Indicators

✅ **Frontend Running**: You see Vite startup messages and can access localhost:5173
✅ **Backend Running**: You see FastAPI/uvicorn startup messages and can access localhost:8080
✅ **Both Working**: Full application functionality at localhost:5173

## Next Steps

1. Start both services as shown above
2. Access the application at http://localhost:5173
3. Begin development with hot reload enabled

Your devcontainer is working perfectly - you just need to start the services!

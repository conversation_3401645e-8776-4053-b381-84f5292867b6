{"version": "2.0.0", "tasks": [{"label": "Start Frontend Dev Server", "type": "shell", "command": "npm run dev", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": [], "runOptions": {"runOn": "default"}}, {"label": "Start Backend Dev Server", "type": "shell", "command": "bash dev.sh", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}/backend"}, "problemMatcher": [], "runOptions": {"runOn": "default"}}, {"label": "Start Both Dev Servers", "dependsOrder": "parallel", "dependsOn": ["Start Frontend Dev Server", "Start Backend Dev Server"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "Install Dependencies", "type": "shell", "command": "npm ci && cd backend && pip3 install -r requirements.txt", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "Build Frontend", "type": "shell", "command": "npm run build", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "Run Tests", "type": "shell", "command": "npm test", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}]}
{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' অথবা অনির্দিষ্টকাল মেয়াদের জন্য '-1' ", "(e.g. `sh webui.sh --api --api-auth username_password`)": "", "(e.g. `sh webui.sh --api`)": "(যেমন `sh webui.sh --api`)", "(latest)": "(সর্বশেষ)", "(leave blank for to use commercial endpoint)": "", "[Last] dddd [at] h:mm A": "", "[Today at] h:mm A": "", "[Yesterday at] h:mm A": "", "{{ models }}": "{{ মডেল}}", "{{COUNT}} Available Tools": "", "{{COUNT}} characters": "", "{{COUNT}} hidden lines": "", "{{COUNT}} Replies": "", "{{COUNT}} words": "", "{{user}}'s Chats": "{{user}}র চ্যাটস", "{{webUIName}} Backend Required": "{{webUIName}} ব্যাকএন্ড আবশ্যক", "*Prompt node ID(s) are required for image generation": "", "A new version (v{{LATEST_VERSION}}) is now available.": "", "A task model is used when performing tasks such as generating titles for chats and web search queries": "চ্যাট এবং ওয়েব অনুসন্ধান প্রশ্নের জন্য শিরোনাম তৈরি করার মতো কাজগুলি সম্পাদন করার সময় একটি টাস্ক মডেল ব্যবহার করা হয়", "a user": "একজ<PERSON> ব্য<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ী", "About": "সম্পর্কে", "Accept autocomplete generation / Jump to prompt variable": "", "Access": "", "Access Control": "", "Accessible to all users": "", "Account": "একাউন্ট", "Account Activation Pending": "", "Accurate information": "সঠিক তথ্য", "Action": "", "Actions": "", "Activate": "", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "", "Active Users": "", "Add": "যোগ করুন", "Add a model ID": "", "Add a short description about what this model does": "এই মডেলটি কী করে সে সম্পর্কে একটি সংক্ষিপ্ত বিবরণ যুক্ত করুন", "Add a tag": "একটি ট্যাগ যোগ করুন", "Add Arena Model": "", "Add Connection": "", "Add Content": "", "Add content here": "", "Add Custom Parameter": "", "Add custom prompt": "একটি কাস্টম প্রম্পট যোগ করুন", "Add Files": "ফাইল যোগ করুন", "Add Group": "", "Add Memory": "মেমোরি যোগ করুন", "Add Model": "মডেল যোগ করুন", "Add Reaction": "", "Add Tag": "", "Add Tags": "ট্যাগ যোগ করুন", "Add text content": "", "Add User": "ইউজার যোগ করুন", "Add User Group": "", "Adjusting these settings will apply changes universally to all users.": "এই সেটিংগুলো পরিবর্তন করলে তা সব ইউজারের উপরেই প্রয়োগ করা হবে", "admin": "এডমিন", "Admin": "", "Admin Panel": "এডমিন প্যানেল", "Admin Settings": "এডমিন সেটিংস", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "", "Advanced Parameters": "এডভান্সড প্যারামিটার্স", "Advanced Params": "অ্যাডভান্সড প্যারাম", "AI": "", "All": "", "All Documents": "সব ডকুমেন্ট", "All models deleted successfully": "", "Allow Call": "", "Allow Chat Controls": "", "Allow Chat Delete": "", "Allow Chat Deletion": "চ্যাট ডিলিট করতে দিন", "Allow Chat Edit": "", "Allow Chat Export": "", "Allow Chat Share": "", "Allow Chat System Prompt": "", "Allow File Upload": "", "Allow Multiple Models in Chat": "", "Allow non-local voices": "", "Allow Speech to Text": "", "Allow Temporary Chat": "", "Allow Text to Speech": "", "Allow User Location": "", "Allow Voice Interruption in Call": "", "Allowed Endpoints": "", "Allowed File Extensions": "", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "", "Already have an account?": "আগে থেকেই একাউন্ট আছে?", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "", "Always": "", "Always Collapse Code Blocks": "", "Always Expand Details": "", "Always Play Notification Sound": "", "Amazing": "", "an assistant": "একটা এসিস্ট্যান্ট", "Analyzed": "", "Analyzing...": "", "and": "এবং", "and {{COUNT}} more": "", "and create a new shared link.": "এবং একটি নতুন শেয়ারে লিংক তৈরি করুন.", "Android": "", "API": "", "API Base URL": "এপিআই বেজ ইউআরএল", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "", "API Key": "এপিআই কোড", "API Key created.": "একটি এপিআই কোড তৈরি করা হয়েছে.", "API Key Endpoint Restrictions": "", "API keys": "এপিআই কোডস", "API Version": "", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "", "April": "আপ্রিল", "Archive": "আর্কাইভ", "Archive All Chats": "আর্কাইভ করুন সকল চ্যাট", "Archived Chats": "চ্যাট ইতিহাস সংরক্ষণাগার", "archived-chat-export": "", "Are you sure you want to clear all memories? This action cannot be undone.": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "", "Are you sure?": "আপনি নিশ্চিত?", "Arena Models": "", "Artifacts": "", "Ask": "", "Ask a question": "", "Assistant": "", "Attach file from knowledge": "", "Attention to detail": "বিস্তারিত বিশেষতা", "Attribute for Mail": "", "Attribute for Username": "", "Audio": "অডিও", "August": "আগস্ট", "Auth": "", "Authenticate": "", "Authentication": "", "Auto": "", "Auto-Copy Response to Clipboard": "রেসপন্সগুলো স্বয়ংক্রিভাবে ক্লিপবোর্ডে কপি হবে", "Auto-playback response": "রেসপন্স অটো-প্লেব্যাক", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "", "AUTOMATIC1111 Api Auth String": "", "AUTOMATIC1111 Base URL": "AUTOMATIC1111 বেজ ইউআরএল", "AUTOMATIC1111 Base URL is required.": "AUTOMATIC1111 বেজ ইউআরএল আবশ্যক", "Available list": "", "Available Tools": "", "available!": "উপলব্ধ!", "Awful": "", "Azure AI Speech": "", "Azure Region": "", "Back": "পেছনে", "Bad Response": "খারাপ প্রতিক্রিয়া", "Banners": "ব্যানার", "Base Model (From)": "বেস মডেল (থেকে)", "Base Model List Cache speeds up access by fetching base models only at startup or on settings save—faster, but may not show recent base model changes.": "", "before": "পূর্ববর্তী", "Being lazy": "অলস হওয়া", "Beta": "", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "Bocha Search API Key": "", "Bold": "", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "", "Brave Search API Key": "সাহসী অনুসন্ধান API কী", "Bullet List": "", "By {{name}}": "", "Bypass Embedding and Retrieval": "", "Bypass Web Loader": "", "Cache Base Model List": "", "Calendar": "", "Call": "", "Call feature is not supported when using Web STT engine": "", "Camera": "", "Cancel": "বাতিল", "Capabilities": "সক্ষমতা", "Capture": "", "Capture Audio": "", "Certificate Path": "", "Change Password": "পাসওয়ার্ড পরিবর্তন করুন", "Channel Name": "", "Channels": "", "Character": "", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "Chat": "চ্যাট", "Chat Background Image": "", "Chat Bubble UI": "চ্যাট বাবল UI", "Chat Controls": "", "Chat direction": "চ্যাট দিকনির<PERSON>দেশ", "Chat Overview": "", "Chat Permissions": "", "Chat Tags Auto-Generation": "", "Chats": "চ্যাটসমূহ", "Check Again": "আবার চেক করুন", "Check for updates": "নতুন আপডেট আছে কিনা চেক করুন", "Checking for updates...": "নতুন আপডেট আছে কিনা চেক করা হচ্ছে...", "Choose a model before saving...": "সেভ করার আগে একটি মডেল নির্বাচন করুন", "Chunk Overlap": "চাঙ্ক ওভারল্যাপ", "Chunk Size": "চাঙ্ক সাইজ", "Ciphers": "", "Citation": "উদ্ধৃতি", "Citations": "", "Clear memory": "", "Clear Memory": "", "click here": "", "Click here for filter guides.": "", "Click here for help.": "সাহায্যের জন্য এখানে ক্লিক করুন", "Click here to": "এখানে ক্লিক করুন", "Click here to download user import template file.": "", "Click here to learn more about faster-whisper and see the available models.": "", "Click here to see available models.": "", "Click here to select": "নির্বাচন করার জন্য এখানে ক্লিক করুন", "Click here to select a csv file.": "একটি csv ফাইল নির্বাচন করার জন্য এখানে ক্লিক করুন", "Click here to select a py file.": "", "Click here to upload a workflow.json file.": "", "click here.": "এখানে ক্লিক করুন", "Click on the user role button to change a user's role.": "ইউজারের পদবি পরিবর্তন করার জন্য ইউজারের পদবি বাটনে ক্লিক করুন", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "", "Clone": "ক্লোন", "Clone Chat": "", "Clone of {{TITLE}}": "", "Close": "বন্ধ", "Close Configure Connection Modal": "", "Close modal": "", "Close settings modal": "", "Code Block": "", "Code execution": "", "Code Execution": "", "Code Execution Engine": "", "Code Execution Timeout": "", "Code formatted successfully": "", "Code Interpreter": "", "Code Interpreter Engine": "", "Code Interpreter Prompt Template": "", "Collapse": "", "Collection": "সংগ্রহ", "Color": "", "ComfyUI": "ComfyUI", "ComfyUI API Key": "", "ComfyUI Base URL": "ComfyUI Base URL", "ComfyUI Base URL is required.": "ComfyUI Base URL আবশ্যক।", "ComfyUI Workflow": "", "ComfyUI Workflow Nodes": "", "Command": "কমান্ড", "Comment": "", "Completions": "", "Concurrent Requests": "সমকালীন অনুরোধ", "Configure": "", "Confirm": "", "Confirm Password": "পাসওয়ার্ড নিশ্চিত করুন", "Confirm your action": "", "Confirm your new password": "", "Connect to your own OpenAI compatible API endpoints.": "", "Connect to your own OpenAPI compatible external tool servers.": "", "Connection failed": "", "Connection successful": "", "Connection Type": "", "Connections": "কানেকশনগুলো", "Connections saved successfully": "", "Connections settings updated": "", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "", "Contact Admin for WebUI Access": "", "Content": "বিষয়বস্তু", "Content Extraction Engine": "", "Continue Response": "যাচাই করুন", "Continue with {{provider}}": "", "Continue with Email": "", "Continue with LDAP": "", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "", "Controls": "", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "", "Copied": "", "Copied link to clipboard": "", "Copied shared chat URL to clipboard!": "শেয়ারকৃত কথা-ব্যবহারের URL ক্লিপবোর্ডে কপি করা হয়েছে!", "Copied to clipboard": "", "Copy": "অনুলিপি", "Copy Formatted Text": "", "Copy last code block": "সর্বশেষ কোড ব্লক কপি করুন", "Copy last response": "সর্বশেষ রেসপন্স কপি করুন", "Copy link": "", "Copy Link": "লিংক ক<PERSON>ি করুন", "Copy to clipboard": "", "Copying to clipboard was successful!": "ক্লিপবোর্ডে কপি করা সফল হয়েছে", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "", "Create": "", "Create a knowledge base": "", "Create a model": "একটি মডেল তৈরি করুন", "Create Account": "একাউন্ট তৈরি করুন", "Create Admin Account": "", "Create Channel": "", "Create Group": "", "Create Knowledge": "", "Create new key": "একটি নতুন কী তৈরি করুন", "Create new secret key": "একটি নতুন সিক্রেট কী তৈরি করুন", "Create Note": "", "Create your first note by clicking on the plus button below.": "", "Created at": "নির<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ল", "Created At": "নির<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ল", "Created by": "", "CSV Import": "", "Ctrl+Enter to Send": "", "Current Model": "বর্তমান মডেল", "Current Password": "বর্তমান পাসওয়ার্ড", "Custom": "কাস্টম", "Custom description enabled": "", "Custom Parameter Name": "", "Custom Parameter Value": "", "Danger Zone": "", "Dark": "ডার্ক", "Database": "ডেটাবেজ", "Datalab Marker API": "", "Datalab Marker API Key required.": "", "DD/MM/YYYY": "", "December": "ডেসেম্বর", "Default": "ডিফল্ট", "Default (Open AI)": "", "Default (SentenceTransformers)": "ডিফল্ট (SentenceTransformers)", "Default description enabled": "", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "", "Default Model": "ডিফল্ট মডেল", "Default model updated": "ডিফল্ট মডেল আপডেট হয়েছে", "Default Models": "", "Default permissions": "", "Default permissions updated successfully": "", "Default Prompt Suggestions": "ডিফল্ট প্রম্পট সাজেশন", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "", "Default User Role": "ইউজারের ডিফল্ট পদবি", "Delete": "মুছে ফেলুন", "Delete a model": "একটি মডেল মুছে ফেলুন", "Delete All Chats": "সব চ্যাট মুছে ফেলুন", "Delete All Models": "", "Delete chat": "চ্যাট মুছে ফেলুন", "Delete Chat": "চ্যাট মুছে ফেলুন", "Delete chat?": "", "Delete folder?": "", "Delete function?": "", "Delete Message": "", "Delete message?": "", "Delete note?": "", "Delete prompt?": "", "delete this link": "এই লিংক মুছে ফেলুন", "Delete tool?": "", "Delete User": "ইউজার মুছে ফেলুন", "Deleted {{deleteModelTag}}": "{{deleteModelTag}} মুছে ফেলা হয়েছে", "Deleted {{name}}": "{{name}} মোছা হয়েছে", "Deleted User": "", "Deployment names are required for Azure OpenAI": "", "Describe Pictures in Documents": "", "Describe your knowledge base and objectives": "", "Description": "বিবরণ", "Detect Artifacts Automatically": "", "Dictate": "", "Didn't fully follow instructions": "ইনস্ট্রাকশন সম্পূর্ণ অনুসরণ করা হয়নি", "Direct": "", "Direct Connections": "", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "", "Direct Tool Servers": "", "Disable Code Interpreter": "", "Disable Image Extraction": "", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "", "Disabled": "", "Discover a function": "", "Discover a model": "একটি মডেল আবিষ্কার করুন", "Discover a prompt": "একটি প্রম্পট খুঁজে বের করুন", "Discover a tool": "", "Discover how to use Open WebUI and seek support from the community.": "", "Discover wonders": "", "Discover, download, and explore custom functions": "", "Discover, download, and explore custom prompts": "কাস্টম প্রম্পটগুলো আবিস্কার, ডাউনলোড এবং এক্সপ্লোর করুন", "Discover, download, and explore custom tools": "", "Discover, download, and explore model presets": "মডেল প্রিসেটগুলো আবিস্কার, ডাউনলোড এবং এক্সপ্লোর করুন", "Display": "", "Display Emoji in Call": "", "Display the username instead of You in the Chat": "চ্যাটে 'আপনি'-র পরবর্তে ইউজারনেম দেখান", "Displays citations in the response": "", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "", "Do not install tools from sources you do not fully trust.": "", "Docling": "", "Docling Server URL required.": "", "Document": "ডকুমেন্ট", "Document Intelligence": "", "Document Intelligence endpoint and key required.": "", "Documentation": "", "Documents": "ডকুমেন্টসমূহ", "does not make any external connections, and your data stays securely on your locally hosted server.": "কোন এক্সটার্নাল কানেকশন তৈরি করে না, এবং আপনার ডেটা আর লোকালি হোস্টেড সার্ভারেই নিরাপদে থাকে।", "Domain Filter List": "", "Don't have an account?": "একাউন্ট নেই?", "don't install random functions from sources you don't trust.": "", "don't install random tools from sources you don't trust.": "", "Don't like the style": "স্টাইল পছন্দ করেন না", "Done": "", "Download": "ডাউনলোড", "Download as SVG": "", "Download canceled": "ডাউনলোড বাতিল করা হয়েছে", "Download Database": "ডেটাবেজ ডাউনলোড করুন", "Drag and drop a file to upload or select a file to view": "", "Draw": "", "Drop any files here to upload": "", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "যেমন '30s','10m'. সময়ের অনুমোদিত অনুমোদিত এককগুলি হচ্ছে 's', 'm', 'h'.", "e.g. \"json\" or a JSON schema": "", "e.g. 60": "", "e.g. A filter to remove profanity from text": "", "e.g. en": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. pdf, docx, txt": "", "e.g. Tools for performing various operations": "", "e.g., 3, 4, 5 (leave blank for default)": "", "e.g., audio/wav,audio/mpeg,video/* (leave blank for defaults, * for all)": "", "e.g., en-US,ja-JP (leave blank for auto-detect)": "", "e.g., westus (leave blank for eastus)": "", "e.g.) en,fr,de": "", "Edit": "এডিট করুন", "Edit Arena Model": "", "Edit Channel": "", "Edit Connection": "", "Edit Default Permissions": "", "Edit Folder": "", "Edit Memory": "", "Edit User": "ইউজার এডিট করুন", "Edit User Group": "", "Edited": "", "Editing": "", "Eject": "", "ElevenLabs": "", "Email": "ইমেইল", "Embark on adventures": "", "Embedding": "", "Embedding Batch Size": "", "Embedding Model": "ইমেজ ইমেবডিং মডেল", "Embedding Model Engine": "ইমেজ ইমেবডিং মডেল ইঞ্জিন", "Embedding model set to \"{{embedding_model}}\"": "ইমেজ ইমেবডিং মডেল সেট করা হয়েছে - \"{{embedding_model}}\"", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Code Execution": "", "Enable Code Interpreter": "", "Enable Community Sharing": "সম্প্রদায় শেয়ারকরণ সক্ষম করুন", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "", "Enable Mirostat sampling for controlling perplexity.": "", "Enable New Sign Ups": "নতুন সাইনআপ চালু করুন", "Enabled": "", "Endpoint URL": "", "Enforce Temporary Chat": "", "Enhance": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "আপনার সিএসভি ফাইলটিতে এই ক্রমে 4 টি কলাম অন্তর্ভুক্ত রয়েছে তা নিশ্চিত করুন: নাম, ইমেল, পাসওয়ার্ড, ভূমিকা।.", "Enter {{role}} message here": "{{role}} মেসেজ এখানে লিখুন", "Enter a detail about yourself for your LLMs to recall": "আপনার এলএলএমগুলি স্মরণ করার জন্য নিজের সম্পর্কে একটি বিশদ লিখুন", "Enter a title for the pending user info overlay. Leave empty for default.": "", "Enter a watermark for the response. Leave empty for none.": "", "Enter api auth string (e.g. username:password)": "", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter BM25 Weight": "", "Enter Bocha Search API Key": "", "Enter Brave Search API Key": "সাহসী অনুসন্ধান API কী লিখুন", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "", "Enter Chunk Overlap": "চাঙ্ক ওভারল্যাপ লিখুন", "Enter Chunk Size": "চাংক সাইজ লিখুন", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "", "Enter Config in JSON format": "", "Enter content for the pending user info overlay. Leave empty for default.": "", "Enter Datalab Marker API Key": "", "Enter description": "", "Enter Docling OCR Engine": "", "Enter Docling OCR Language(s)": "", "Enter Docling Server URL": "", "Enter Document Intelligence Endpoint": "", "Enter Document Intelligence Key": "", "Enter domains separated by commas (e.g., example.com,site.org)": "", "Enter Exa API Key": "", "Enter External Document Loader API Key": "", "Enter External Document Loader URL": "", "Enter External Web Loader API Key": "", "Enter External Web Loader URL": "", "Enter External Web Search API Key": "", "Enter External Web Search URL": "", "Enter Firecrawl API Base URL": "", "Enter Firecrawl API Key": "", "Enter folder name": "", "Enter Github Raw URL": "গিটহাব কাঁচা URL লিখুন", "Enter Google PSE API Key": "গুগল পিএসই এপিআই কী লিখুন", "Enter Google PSE Engine Id": "গুগল পিএসই ইঞ্জিন আইডি লিখুন", "Enter Image Size (e.g. 512x512)": "ছবির মা<PERSON> লিখুন (যেমন 512x512)", "Enter Jina API Key": "", "Enter Jupyter Password": "", "Enter Jupyter Token": "", "Enter Jupyter URL": "", "Enter Kagi Search API Key": "", "Enter Key Behavior": "", "Enter language codes": "ল্যাঙ্গুয়েজ কোড লিখুন", "Enter Mistral API Key": "", "Enter Model ID": "", "Enter model tag (e.g. {{modelTag}})": "মডেল ট্যাগ লিখুন (e.g. {{modelTag}})", "Enter Mojeek Search API Key": "", "Enter name": "", "Enter New Password": "", "Enter Number of Steps (e.g. 50)": "ধাপের সংখ্যা দিন (যেমন: 50)", "Enter Perplexity API Key": "", "Enter Playwright Timeout": "", "Enter Playwright WebSocket URL": "", "Enter proxy URL (e.g. **************************:port)": "", "Enter reasoning effort": "", "Enter Sampler (e.g. Euler a)": "", "Enter Scheduler (e.g. Karras)": "", "Enter Score": "স্কোর দিন", "Enter SearchApi API Key": "", "Enter SearchApi Engine": "", "Enter Searxng Query URL": "Searxng ক্যোয়ারী URL লিখুন", "Enter Seed": "", "Enter SerpApi API Key": "", "Enter SerpApi Engine": "", "Enter Serper API Key": "<PERSON><PERSON> API কী লিখুন", "Enter Serply API Key": "", "Enter Serpstack API Key": "Serpstack API কী লিখুন", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter Sougou Search API sID": "", "Enter Sougou Search API SK": "", "Enter stop sequence": "স্টপ সিকোয়েন্স লিখুন", "Enter system prompt": "", "Enter system prompt here": "", "Enter Tavily API Key": "", "Enter Tavily Extract Depth": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter the URL of the function to import": "", "Enter the URL to import": "", "Enter Tika Server URL": "", "Enter timeout in seconds": "", "Enter to Send": "", "Enter Top K": "Top K লিখুন", "Enter Top K Reranker": "", "Enter URL (e.g. http://127.0.0.1:7860/)": "ইউআরএল দিন (যেমন http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "ইউআরএল দিন (যেমন http://localhost:11434)", "Enter Yacy Password": "", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "", "Enter Yacy Username": "", "Enter your current password": "", "Enter Your Email": "আপন<PERSON><PERSON> ই<PERSON>েইল লিখুন", "Enter Your Full Name": "আপনার পূর্ণ নাম লিখুন", "Enter your message": "", "Enter your name": "", "Enter Your Name": "", "Enter your new password": "", "Enter Your Password": "আপনার পাসওয়ার্ড লিখুন", "Enter Your Role": "আ<PERSON><PERSON><PERSON><PERSON> রোল লিখুন", "Enter Your Username": "", "Enter your webhook URL": "", "Error": "ত্রুটি", "ERROR": "", "Error accessing Google Drive: {{error}}": "", "Error accessing media devices.": "", "Error starting recording.": "", "Error unloading model: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "", "Everyone": "", "Exa API Key": "", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: mail": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "", "Exclude": "", "Execute code for analysis": "", "Executing **{{NAME}}**...": "", "Expand": "", "Experimental": "পরিক্ষামূলক", "Explain": "", "Explore the cosmos": "", "Export": "রপ্ত<PERSON>নি", "Export All Archived Chats": "", "Export All Chats (All Users)": "সব চ্যাট এক্সপোর্ট করুন (সব ইউজারের)", "Export chat (.json)": "", "Export Chats": "চ্যাটগুলো এক্সপোর্ট করুন", "Export Config to JSON File": "", "Export Functions": "", "Export Models": "রপ্তানি মডেল", "Export Presets": "", "Export Prompt Suggestions": "", "Export Prompts": "প্রম্পটগুলো একপোর্ট করুন", "Export to CSV": "", "Export Tools": "", "External": "", "External Document Loader URL required.": "", "External Task Model": "", "External Web Loader API Key": "", "External Web Loader URL": "", "External Web Search API Key": "", "External Web Search URL": "", "Failed to add file.": "", "Failed to connect to {{URL}} OpenAPI tool server": "", "Failed to copy link": "", "Failed to create API Key.": "API Key তৈরি করা যায়নি।", "Failed to delete note": "", "Failed to extract content from the file: {{error}}": "", "Failed to extract content from the file.": "", "Failed to fetch models": "", "Failed to generate title": "", "Failed to load file content.": "", "Failed to read clipboard contents": "ক্লিপবোর্ডের বিষয়বস্তু পড়া সম্ভব হয়নি", "Failed to save connections": "", "Failed to save models configuration": "", "Failed to update settings": "", "Failed to upload file.": "", "Features": "", "Features Permissions": "", "February": "ফেব্রুয়ারি", "Feedback Details": "", "Feedback History": "", "Feedbacks": "", "Feel free to add specific details": "নির্দিষ্ট বিবরণ যোগ করতে বিনা দ্বিধায়", "File": "", "File added successfully.": "", "File content updated successfully.": "", "File Mode": "ফাইল মোড", "File not found.": "ফাইল পাওয়া যায়নি", "File removed successfully.": "", "File size should not exceed {{maxSize}} MB.": "", "File Upload": "", "File uploaded successfully": "", "Files": "", "Filter": "", "Filter is now globally disabled": "", "Filter is now globally enabled": "", "Filters": "", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "ফিঙ্গারপ্রিন্ট স্পুফিং ধরা পড়েছে: অ্যাভাটার হিসেবে নামের আদ্যক্ষর ব্যবহার করা যাচ্ছে না। ডিফল্ট প্রোফাইল পিকচারে ফিরিয়ে নেয়া হচ্ছে।", "Firecrawl API Base URL": "", "Firecrawl API Key": "", "Fluidly stream large external response chunks": "বড় এক্সটার্নাল রেসপন্স চাঙ্কগুলো মসৃণভাবে প্রবাহিত করুন", "Focus chat input": "চ্যাট ইনপুট ফোকাস করুন", "Folder deleted successfully": "", "Folder Name": "", "Folder name cannot be empty.": "", "Folder name updated successfully": "", "Folder updated successfully": "", "Follow up": "", "Follow Up Generation": "", "Follow Up Generation Prompt": "", "Follow-Up Auto-Generation": "", "Followed instructions perfectly": "নির্দেশাবলী নিখুঁতভাবে অনুসরণ করা হয়েছে", "Force OCR": "", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "", "Forge new paths": "", "Form": "", "Format your variables using brackets like this:": "", "Forwards system user session credentials to authenticate": "", "Full Context Mode": "", "Function": "", "Function Calling": "", "Function created successfully": "", "Function deleted successfully": "", "Function Description": "", "Function ID": "", "Function imported successfully": "", "Function is now globally disabled": "", "Function is now globally enabled": "", "Function Name": "", "Function updated successfully": "", "Functions": "", "Functions allow arbitrary code execution.": "", "Functions imported successfully": "", "Gemini": "", "Gemini API Config": "", "Gemini API Key is required.": "", "General": "সাধারণ", "Generate": "", "Generate an image": "", "Generate Image": "", "Generate prompt pair": "", "Generating search query": "অনুসন্ধান ক্যোয়ারী তৈরি করা হচ্ছে", "Generating...": "", "Get information on {{name}} in the UI": "", "Get started": "", "Get started with {{WEBUI_NAME}}": "", "Global": "", "Good Response": "ভালো সাড়া", "Google Drive": "", "Google PSE API Key": "গুগল পিএসই এপিআই কী", "Google PSE Engine Id": "গুগল পিএসই ইঞ্জিন আইডি", "Group created successfully": "", "Group deleted successfully": "", "Group Description": "", "Group Name": "", "Group updated successfully": "", "Groups": "", "H1": "", "H2": "", "H3": "", "Haptic Feedback": "", "Hello, {{name}}": "হ্যালো, {{name}}", "Help": "সহায়তা", "Help us create the best community leaderboard by sharing your feedback history!": "", "Hex Color": "", "Hex Color - Leave empty for default color": "", "Hide": "ল<PERSON><PERSON><PERSON><PERSON>", "Hide from Sidebar": "", "Hide Model": "", "High Contrast Mode": "", "Home": "", "Host": "", "How can I help you today?": "আপনাকে আজ কিভাবে সাহায্য করতে পারি?", "How would you rate this response?": "", "HTML": "", "Hybrid Search": "হাইব্রিড অনুসন্ধান", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "", "ID": "", "iframe Sandbox Allow Forms": "", "iframe Sandbox Allow Same Origin": "", "Ignite curiosity": "", "Image": "", "Image Compression": "", "Image Compression Height": "", "Image Compression Width": "", "Image Generation": "", "Image Generation (Experimental)": "ইমেজ জেনারেশন (পরিক্ষামূলক)", "Image Generation Engine": "ইমেজ জেনারেশন ইঞ্জিন", "Image Max Compression Size": "", "Image Max Compression Size height": "", "Image Max Compression Size width": "", "Image Prompt Generation": "", "Image Prompt Generation Prompt": "", "Image Settings": "ছবির সেটিংসমূহ", "Images": "ছবিসমূহ", "Import": "", "Import Chats": "চ্যাটগুলি ইমপোর্ট করুন", "Import Config from JSON File": "", "Import From Link": "", "Import Functions": "", "Import Models": "মডেল আমদানি করুন", "Import Notes": "", "Import Presets": "", "Import Prompt Suggestions": "", "Import Prompts": "প্রম্পটগুলো ইমপোর্ট করুন", "Import Tools": "", "Include": "", "Include `--api-auth` flag when running stable-diffusion-webui": "", "Include `--api` flag when running stable-diffusion-webui": "stable-diffusion-webui চালু করার সময় `--api` ফ্ল্যাগ সংযুক্ত করুন", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "", "Info": "তথ্য", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "", "Input commands": "ইনপুট কমান্ডস", "Input Variables": "", "Insert": "", "Insert Prompt as Rich Text": "", "Install from Github URL": "<PERSON><PERSON><PERSON>RL থেকে ইনস্টল করুন", "Instant Auto-Send After Voice Transcription": "", "Integration": "", "Interface": "ইন্টারফেস", "Invalid file content": "", "Invalid file format.": "", "Invalid JSON file": "", "Invalid Tag": "অবৈধ ট্যাগ", "is typing...": "", "Italic": "", "January": "জান<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Jina API Key": "", "join our Discord for help.": "সাহায্যের জন্য আমাদের Discord-এ যুক্ত হোন", "JSON": "JSON", "JSON Preview": "JSON প্রিভিউ", "July": "জুলাই", "June": "জুন", "Jupyter Auth": "", "Jupyter URL": "", "JWT Expiration": "JWT-র মেয়াদ", "JWT Token": "JWT টোকেন", "Kagi Search API Key": "", "Keep in Sidebar": "", "Key": "", "Keyboard shortcuts": "কিবোর্ড শর্টকাটসমূহ", "Knowledge": "", "Knowledge Access": "", "Knowledge Base": "", "Knowledge created successfully.": "", "Knowledge deleted successfully.": "", "Knowledge Public Sharing": "", "Knowledge reset successfully.": "", "Knowledge updated successfully": "", "Kokoro.js (Browser)": "", "Kokoro.js Dtype": "", "Label": "", "Landing Page Mode": "", "Language": "ভাষা", "Language Locales": "", "Languages": "", "Last Active": "সর্বশেষ সক্রিয়", "Last Modified": "", "Last reply": "", "LDAP": "", "LDAP server updated": "", "Leaderboard": "", "Learn more about OpenAPI tool servers.": "", "Leave empty for no compression": "", "Leave empty for unlimited": "", "Leave empty to include all models from \"{{url}}\" endpoint": "", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{url}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "", "Leave empty to use the default prompt, or enter a custom prompt": "", "Leave model field empty to use the default model.": "", "License": "", "Light": "লাইট", "Listening...": "", "Llama.cpp": "", "LLMs can make mistakes. Verify important information.": "LLM ভুল করতে পারে। গুরুত্বপূর্ণ তথ্য যাচাই করে নিন।", "Loader": "", "Loading Kokoro.js...": "", "Local": "", "Local Task Model": "", "Location access not allowed": "", "Lost": "", "LTR": "LTR", "Made by Open WebUI Community": "OpenWebUI কমিউনিটিকর্তৃক নির্মিত", "Make password visible in the user interface": "", "Make sure to enclose them with": "এটা দিয়ে বন্ধনী দিতে ভুলবেন না", "Make sure to export a workflow.json file as API format from ComfyUI.": "", "Manage": "", "Manage Direct Connections": "", "Manage Models": "", "Manage Ollama": "", "Manage Ollama API Connections": "", "Manage OpenAI API Connections": "", "Manage Pipelines": "পাইপল<PERSON>ইন পরিচালনা করুন", "Manage Tool Servers": "", "March": "মা<PERSON><PERSON>চ", "Markdown": "", "Markdown (Header)": "", "Max Speakers": "", "Max Upload Count": "", "Max Upload Size": "", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "একসঙ্গে সর্বোচ্চ তিনটি মডেল ডাউনলোড করা যায়। দয়া করে পরে আবার চেষ্টা করুন।", "May": "মে", "Memories accessible by LLMs will be shown here.": "LLMs দ্বারা অ্যাক্সেসযোগ্য মেমোরিগুলি এখানে দেখানো হবে।", "Memory": "মেম<PERSON><PERSON>ি", "Memory added successfully": "", "Memory cleared successfully": "", "Memory deleted successfully": "", "Memory updated successfully": "", "Merge Responses": "", "Merged Response": "একত্রিত প্রতিক্রিয়া ফলাফল", "Message rating should be enabled to use this feature": "", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "আপনার লিঙ্ক তৈরি করার পরে আপনার পাঠানো বার্তাগুলি শেয়ার করা হবে না। ইউআরএল ব্যবহারকারীরা শেয়ার করা চ্যাট দেখতে পারবেন।", "Microsoft OneDrive": "", "Microsoft OneDrive (personal)": "", "Microsoft OneDrive (work/school)": "", "Mistral OCR": "", "Mistral OCR API Key required.": "", "Model": "", "Model '{{modelName}}' has been successfully downloaded.": "'{{modelName}}' মডেল সফলভাবে ডাউনলোড হয়েছে।", "Model '{{modelTag}}' is already in queue for downloading.": "{{modelTag}} ডাউনলোডের জন্য আগে থেকেই অপেক্ষমান আছে।", "Model {{modelId}} not found": "{{modelId}} মডেল পাওয়া যায়নি", "Model {{modelName}} is not vision capable": "মডেল {{modelName}} দৃষ্টি সক্ষম নয়", "Model {{name}} is now {{status}}": "মডেল {{name}} এখ<PERSON> {{status}}", "Model {{name}} is now hidden": "", "Model {{name}} is now visible": "", "Model accepts file inputs": "", "Model accepts image inputs": "", "Model can execute code and perform calculations": "", "Model can generate images based on text prompts": "", "Model can search the web for information": "", "Model created successfully!": "", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "মডেল ফাইলসিস্টেম পাথ পাওয়া গেছে। আপডেটের জন্য মডেলের শর্টনেম আবশ্যক, এগিয়ে যাওয়া যাচ্ছে না।", "Model Filtering": "", "Model ID": "মডেল ID", "Model IDs": "", "Model Name": "", "Model not selected": "মডেল নির্বাচন করা হয়নি", "Model Params": "মডেল প্যারাম", "Model Permissions": "", "Model unloaded successfully": "", "Model updated successfully": "", "Model(s) do not support file upload": "", "Modelfile Content": "মডেলফাইল কনটেন্ট", "Models": "মডেলসমূহ", "Models Access": "", "Models configuration saved successfully": "", "Models Public Sharing": "", "Mojeek Search API Key": "", "more": "", "More": "<PERSON><PERSON><PERSON>", "Name": "নাম", "Name your knowledge base": "", "Native": "", "New Chat": "নতুন চ্যাট", "New Folder": "", "New Function": "", "New Note": "", "New Password": "নতুন পাসওয়ার্ড", "New Tool": "", "new-channel": "", "Next message": "", "No chats found for this user.": "", "No chats found.": "", "No content": "", "No content found": "", "No content found in file.": "", "No content to speak": "", "No distance available": "", "No feedbacks found": "", "No file selected": "", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "", "No inference engine with management support found": "", "No knowledge found": "", "No memories to clear": "", "No model IDs": "", "No models found": "", "No models selected": "", "No Notes": "", "No results found": "কোন ফলাফল পাওয়া যায়নি", "No search query generated": "কোনও অনুসন্ধান ক্যোয়ারী উত্পন্ন হয়নি", "No source available": "কোন উৎস পাওয়া যায়নি", "No users were found.": "", "No valves to update": "", "None": "কোনোটিই নয়", "Not factually correct": "তথ্যগত দিক থেকে সঠিক নয়", "Not helpful": "", "Note deleted successfully": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "দ্রষ্টব্য: আপনি যদি ন্যূনতম স্কোর সেট করেন তবে অনুসন্ধানটি কেবলমাত্র ন্যূনতম স্কোরের চেয়ে বেশি বা সমান স্কোর সহ নথিগুলি ফেরত দেবে।", "Notes": "", "Notification Sound": "", "Notification Webhook": "", "Notifications": "নোটিফিকেশনসমূহ", "November": "নভেম্বর", "OAuth ID": "", "October": "অক্টোবর", "Off": "বন্ধ", "Okay, Let's Go!": "ঠিক আছে, চলুন যাই!", "OLED Dark": "OLED ডার্ক", "Ollama": "Ollama", "Ollama API": "Ollama API", "Ollama API settings updated": "", "Ollama Version": "Ollama ভার্সন", "On": "চালু", "OneDrive": "", "Only active when \"Paste Large Text as File\" setting is toggled on.": "", "Only active when the chat input is in focus and an LLM is generating a response.": "", "Only alphanumeric characters and hyphens are allowed": "", "Only alphanumeric characters and hyphens are allowed in the command string.": "কমান্ড স্ট্রিং-এ শুধুমাত্র ইংরেজি অক্ষর, সংখ্যা এবং হাইফেন ব্যবহার করা যাবে।", "Only collections can be edited, create a new knowledge base to edit/add documents.": "", "Only markdown files are allowed": "", "Only select users and groups with permission can access": "", "Oops! Looks like the URL is invalid. Please double-check and try again.": "ওহ, মনে হচ্ছে ইউআরএলটা ইনভ্যালিড। দয়া করে আর চেক করে চেষ্টা করুন।", "Oops! There are files still uploading. Please wait for the upload to complete.": "", "Oops! There was an error in the previous response.": "", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "আপনি একটা আনসাপোর্টেড পদ্ধতি (শুধু ফ্রন্টএন্ড) ব্যবহার করছেন। দয়া করে WebUI ব্যাকএন্ড থেকে চালনা করুন।", "Open file": "", "Open in full screen": "", "Open modal to configure connection": "", "Open new chat": "নতুন চ্যাট খুলুন", "Open WebUI can use tools provided by any OpenAPI server.": "", "Open WebUI uses faster-whisper internally.": "", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "", "OpenAI": "OpenAI", "OpenAI API": "OpenAI এপিআই", "OpenAI API Config": "OpenAI এপিআই কনফিগ", "OpenAI API Key is required.": "OpenAI API কোড আবশ্যক", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "OpenAI URL/Key আবশ্যক", "openapi.json URL or Path": "", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "", "or": "অথবা", "Ordered List": "", "Organize your users": "", "Other": "অন্যান্য", "OUTPUT": "", "Output format": "", "Output Format": "", "Overview": "", "page": "", "Paginate": "", "Parameters": "", "Password": "পাসওয়ার্ড", "Paste Large Text as File": "", "PDF document (.pdf)": "PDF ডকুমেন্ট (.pdf)", "PDF Extract Images (OCR)": "পিডিএফ এর ছবি থেকে লেখা বের করুন (OCR)", "pending": "অপেক্ষমান", "Pending": "", "Pending User Overlay Content": "", "Pending User Overlay Title": "", "Permission denied when accessing media devices": "", "Permission denied when accessing microphone": "", "Permission denied when accessing microphone: {{error}}": "মাইক্রোফোন ব্যবহারের অনুমতি পাওয়া যায়নি: {{error}}", "Permissions": "", "Perplexity API Key": "", "Perplexity Model": "", "Perplexity Search Context Usage": "", "Personalization": "ডিজিটাল বাংলা", "Picture Description API Config": "", "Picture Description Local Config": "", "Picture Description Mode": "", "Pin": "", "Pinned": "", "Pioneer insights": "", "Pipe": "", "Pipeline deleted successfully": "", "Pipeline downloaded successfully": "", "Pipelines": "পাইপল<PERSON>ইন", "Pipelines Not Detected": "", "Pipelines Valves": "পাইপলাইন ভালভ", "Plain text (.md)": "", "Plain text (.txt)": "প্লায়েন টেক্সট (.txt)", "Playground": "খেলাঘর", "Playwright Timeout (ms)": "", "Playwright WebSocket URL": "", "Please carefully review the following warnings:": "", "Please do not close the settings page while loading the model.": "", "Please enter a prompt": "", "Please enter a valid path": "", "Please enter a valid URL": "", "Please fill in all fields.": "", "Please select a model first.": "", "Please select a model.": "", "Please select a reason": "", "Port": "", "Positive attitude": "পজিটিভ আক্রমণ", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Prevent file creation": "", "Preview": "", "Previous 30 days": "পূর্ব ৩০ দিন", "Previous 7 days": "পূর্ব ৭ দিন", "Previous message": "", "Private": "", "Profile Image": "প্রোফাইল ইমেজ", "Prompt": "", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "প্রম্প্ট (উদাহরণস্বরূপ, আমি রোমান ইমপার্টের সম্পর্কে একটি উপস্থিতি জানতে বল)", "Prompt Autocompletion": "", "Prompt Content": "প্রম্পট কন্টেন্ট", "Prompt created successfully": "", "Prompt suggestions": "প্রম্পট সাজেশনসমূহ", "Prompt updated successfully": "", "Prompts": "প্রম্পটসমূহ", "Prompts Access": "", "Prompts Public Sharing": "", "Public": "", "Pull \"{{searchValue}}\" from Ollama.com": "Ollama.com থেকে \"{{searchValue}}\" টানুন", "Pull a model from Ollama.com": "Ollama.com থেকে একটি টেনে আনুন আনুন", "Query Generation Prompt": "", "RAG Template": "RAG টেম্পলেট", "Rating": "", "Re-rank models by topic similarity": "", "Read": "", "Read Aloud": "পড়াশোনা করুন", "Reason": "", "Reasoning Effort": "", "Record": "", "Record voice": "ভয়েস রেকর্ড করুন", "Redirecting you to Open WebUI Community": "আপনাকে OpenWebUI কমিউনিটিতে পাঠানো হচ্ছে", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "", "References from": "", "Refused when it shouldn't have": "যদি উপযুক্ত নয়, তবে রেজিগেনেট করা হচ্ছে", "Regenerate": "রেজিগেনেট করুন", "Reindex": "", "Reindex Knowledge Base Vectors": "", "Release Notes": "রিলিজ নোটসমূহ", "Releases": "", "Relevance": "", "Relevance Threshold": "", "Remember Dismissal": "", "Remove": "রিমুভ করুন", "Remove {{MODELID}} from list.": "", "Remove file": "", "Remove File": "", "Remove image": "", "Remove Model": "মডেল রিমুভ করুন", "Remove this tag from list": "", "Rename": "রেনেম", "Reorder Models": "", "Reply in Thread": "", "Reranking Engine": "", "Reranking Model": "রির্যাক্টিং মডেল", "Reset": "", "Reset All Models": "", "Reset Upload Directory": "", "Reset Vector Storage/Knowledge": "", "Reset view": "", "Response": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "", "Response splitting": "", "Response Watermark": "", "Result": "", "Retrieval": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "", "RK": "", "Role": "পদবি", "Rosé Pine": "রোজ পাইন", "Rosé Pine Dawn": "ভোরের রোজ পাইন", "RTL": "RTL", "Run": "", "Running": "", "Save": "সংরক্ষণ", "Save & Create": "সংরক্ষণ এবং তৈরি করুন", "Save & Update": "সংরক্ষণ এবং আপডেট করুন", "Save As Copy": "", "Save Tag": "", "Saved": "", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "মাধ্যমে", "Scroll On Branch Change": "", "Search": "অনুসন্ধান", "Search a model": "মডেল অনুসন্ধান করুন", "Search Base": "", "Search Chats": "চ্যাট অনুসন্ধান করুন", "Search Collection": "", "Search Filters": "", "search for tags": "", "Search Functions": "", "Search Knowledge": "", "Search Models": "অনুসন্ধান মডেল", "Search Notes": "", "Search options": "", "Search Prompts": "প্রম্পটসমূহ অনুসন্ধান করুন", "Search Result Count": "অনুসন্ধানের ফলাফল গণনা", "Search the internet": "", "Search Tools": "", "SearchApi API Key": "", "SearchApi Engine": "", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "", "Searching Knowledge for \"{{searchQuery}}\"": "", "Searching the web...": "", "Searxng Query URL": "Searxng ক্যোয়ারী URL", "See readme.md for instructions": "নির্দেশিক<PERSON><PERSON> জন্য readme.md দেখুন", "See what's new": "নতুন কী আছে দেখুন", "Seed": "সীড", "Select a base model": "একটি বেস মডেল নির্বাচন করুন", "Select a engine": "", "Select a function": "", "Select a group": "", "Select a model": "একটি মডেল নির্বাচন করুন", "Select a pipeline": "একটি পাইপলাইন নির্বাচন করুন", "Select a pipeline url": "একটি পাইপলাইন URL নির্বাচন করুন", "Select a tool": "", "Select an auth method": "", "Select an Ollama instance": "", "Select Engine": "", "Select Knowledge": "", "Select only one model to call": "", "Selected model(s) do not support image inputs": "নির্বাচিত মডেল(গুলি) চিত্র ইনপুট সমর্থন করে না", "Semantic distance to query": "", "Send": "পাঠান", "Send a Message": "একটি মেসেজ পাঠান", "Send message": "মেসেজ পাঠান", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "", "September": "সেপ্টেম্বর", "SerpApi API Key": "", "SerpApi Engine": "", "Serper API Key": "Serper API Key", "Serply API Key": "", "Serpstack API Key": "Serpstack API Key", "Server connection verified": "সার্ভার কানেকশন যাচাই করা হয়েছে", "Set as default": "ডিফল্ট হিসেবে নির্ধারণ করুন", "Set CFG Scale": "", "Set Default Model": "ডিফল্ট মডেল নির্ধারণ করুন", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "ইমেম্বিং মডেল নির্ধারণ করুন (উদাহরণ {{model}})", "Set Image Size": "ছবির সাইজ নির্ধারণ করুন", "Set reranking model (e.g. {{model}})": "রি-র্যাংকিং মডেল নির্ধারণ করুন (উদাহরণ {{model}})", "Set Sampler": "", "Set Scheduler": "", "Set Steps": "পরবর্তী ধাপসমূহ", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "কন্ঠস্বর নির্ধারণ করুন", "Set whisper model": "", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets how far back for the model to look back to prevent repetition.": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "", "Sets the size of the context window used to generate the next token.": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "সেটিংসমূহ", "Settings saved successfully!": "সেটিংগুলো সফলভাবে সংরক্ষিত হয়েছে", "Share": "শেয়ার কর<PERSON>ন", "Share Chat": "চ্যাট শেয়ার করুন", "Share to Open WebUI Community": "OpenWebUI কমিউনিটিতে শেয়ার করুন", "Sharing Permissions": "", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "", "Show": "দেখ<PERSON>ন", "Show \"What's New\" modal on login": "", "Show Admin Details in Account Pending Overlay": "", "Show All": "", "Show image preview": "", "Show Less": "", "Show Model": "", "Show shortcuts": "শর্টকাটগুলো দেখান", "Show your support!": "", "Showcased creativity": "সৃজনশীলতা প্রদর্শন", "Sign in": "সা<PERSON><PERSON> ইন", "Sign in to {{WEBUI_NAME}}": "", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "সাইন আউট", "Sign up": "সাইন আপ", "Sign up to {{WEBUI_NAME}}": "", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to True.": "", "Signing in to {{WEBUI_NAME}}": "", "sk-1234": "", "Skip Cache": "", "Skip the cache and re-run the inference. Defaults to False.": "", "Sougou Search API sID": "", "Sougou Search API SK": "", "Source": "উৎস", "Speech Playback Speed": "", "Speech recognition error: {{error}}": "স্পিচ রিকগনিশনে সমস্যা: {{error}}", "Speech-to-Text": "", "Speech-to-Text Engine": "স্পিচ-টু-টেক্সট ইঞ্জিন", "Stop": "", "Stop Generating": "", "Stop Sequence": "সিকোয়েন্স থামান", "Stream Chat Response": "", "Strikethrough": "", "Strip Existing OCR": "", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "", "STT Model": "", "STT Settings": "STT সেটিংস", "Stylized PDF Export": "", "Subtitle (e.g. about the Roman Empire)": "সাবটাইটল (র<PERSON><PERSON><PERSON>ন ইম্পার্টের সম্পর্কে)", "Success": "সফল", "Successfully updated.": "সফলভাবে আপডেট হয়েছে", "Suggested": "প্রস্তাবিত", "Support": "", "Support this plugin:": "", "Supported MIME Types": "", "Sync directory": "", "System": "সিস্টেম", "System Instructions": "", "System Prompt": "সিস্টেম প্রম্পট", "Tags": "", "Tags Generation": "", "Tags Generation Prompt": "", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "", "Talk to model": "", "Tap to interrupt": "", "Task List": "", "Task Model": "", "Tasks": "", "Tavily API Key": "", "Tavily Extract Depth": "", "Tell us more:": "আরও বলুন:", "Temperature": "তাপমাত্রা", "Temporary Chat": "", "Text Splitter": "", "Text-to-Speech": "", "Text-to-Speech Engine": "টেক্সট-টু-স্পিচ ইঞ্জিন", "Thanks for your feedback!": "আপনার মতামত ধন্যবাদ!", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "", "The format to return a response in. Format can be json or a JSON schema.": "", "The height in pixels to compress images to. Leave empty for no compression.": "", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "", "The LDAP attribute that maps to the mail that users use to sign in.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "স্কোর একটি 0.0 (0%) এবং 1.0 (100%) এর মধ্যে একটি মান হওয়া উচিত।", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "", "The width in pixels to compress images to. Leave empty for no compression.": "", "Theme": "থিম", "Thinking...": "", "This action cannot be undone. Do you wish to continue?": "", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "", "This chat won't appear in history and your messages will not be saved.": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "এটা নিশ্চিত করে যে, আপনার গুরুত্বপূর্ণ আলোচনা নিরাপদে আপনার ব্যাকএন্ড ডেটাবেজে সংরক্ষিত আছে। ধন্যবাদ!", "This feature is experimental and may be modified or discontinued without notice.": "", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "", "This model is not publicly available. Please select another model.": "", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "", "This response was generated by \"{{model}}\"": "", "This will delete": "", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "", "This will delete all models including custom models": "", "This will delete all models including custom models and cannot be undone.": "", "This will reset the knowledge base and sync all files. Do you wish to continue?": "", "Thorough explanation": "পুঙ্খানুপুঙ্খ ব্যাখ্যা", "Thought for {{DURATION}}": "", "Thought for {{DURATION}} seconds": "", "Tika": "", "Tika Server URL required.": "", "Tiktoken": "", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "পরামর্শ: একাধিক ভেরিয়েবল স্লট একের পর এক রিপ্লেস করার জন্য চ্যাট ইনপুটে কিবোর্ডের Tab বাটন ব্যবহার করুন।", "Title": "শির<PERSON><PERSON><PERSON><PERSON><PERSON>", "Title (e.g. Tell me a fun fact)": "শির<PERSON><PERSON><PERSON>া<PERSON> (একটি উপস্থিতি বিবরণ জানান)", "Title Auto-Generation": "স্বয়ংক্রিয় শিরোনামগঠন", "Title cannot be an empty string.": "শিরোনাম অবশ্যই একটি পাশাপাশি শব্দ হতে হবে।", "Title Generation": "", "Title Generation Prompt": "শিরোনামগঠন প্রম্পট", "TLS": "", "To access the available model names for downloading,": "ডাউনলোডের জন্য এভেইলএবল মডেলের নামগুলো এক্সেস করতে,", "To access the GGUF models available for downloading,": "ডাউলোডের জন্য এভেইলএবল GGUF মডেলগুলো এক্সেস করতে,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "", "To select actions here, add them to the \"Functions\" workspace first.": "", "To select filters here, add them to the \"Functions\" workspace first.": "", "To select toolkits here, add them to the \"Tools\" workspace first.": "", "Toast notifications for new updates": "", "Today": "আজ", "Toggle search": "", "Toggle settings": "সেটিংস টোগল", "Toggle sidebar": "সাইডবার টোগল", "Toggle whether current connection is active.": "", "Token": "", "Too verbose": "", "Tool created successfully": "", "Tool deleted successfully": "", "Tool Description": "", "Tool ID": "", "Tool imported successfully": "", "Tool Name": "", "Tool Servers": "", "Tool updated successfully": "", "Tools": "", "Tools Access": "", "Tools are a function calling system with arbitrary code execution": "", "Tools Function Calling Prompt": "", "Tools have a function calling system that allows arbitrary code execution.": "", "Tools Public Sharing": "", "Top K": "Top K", "Top K Reranker": "", "Transformers": "", "Trouble accessing Ollama?": "<PERSON><PERSON><PERSON> এক্সেস করতে সমস্যা হচ্ছে?", "Trust Proxy Environment": "", "TTS Model": "", "TTS Settings": "TTS সেটিংসমূহ", "TTS Voice": "", "Type": "টা<PERSON>প", "Type Hugging Face Resolve (Download) URL": "Hugging Face থেকে ডাউনলোড করার ইউআরএল টাইপ করুন", "Uh-oh! There was an issue with the response.": "", "UI": "", "Unarchive All": "", "Unarchive All Archived Chats": "", "Unarchive Chat": "", "Underline": "", "Unloads {{FROM_NOW}}": "", "Unlock mysteries": "", "Unpin": "", "Unravel secrets": "", "Untagged": "", "Untitled": "", "Update": "", "Update and Copy Link": "আপডেট এবং লিংক কপি করুন", "Update for the latest features and improvements.": "", "Update password": "পাসওয়ার্ড আপডেট করুন", "Updated": "", "Updated at": "", "Updated At": "", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "", "Upload": "", "Upload a GGUF model": "একটি GGUF মডেল আপলোড করুন", "Upload Audio": "", "Upload directory": "", "Upload files": "", "Upload Files": "ফাইল আপলোড করুন", "Upload Pipeline": "", "Upload Progress": "আপলোড হচ্ছে", "URL": "", "URL Mode": "ইউআরএল মোড", "Usage": "", "Use '#' in the prompt input to load and include your knowledge.": "", "Use Gravatar": "Gravatar ব্য<PERSON><PERSON><PERSON>র কর<PERSON>ন", "Use groups to group your users and assign permissions.": "", "Use Initials": "নামের আদ্যক্ষর ব্যবহার করুন", "Use LLM": "", "Use no proxy to fetch page contents.": "", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "", "user": "ব্<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "User": "", "User location successfully retrieved.": "", "User menu": "", "User Webhooks": "", "Username": "", "Users": "ব্যাবহারকারীগণ", "Using the default arena model with all models. Click the plus button to add custom models.": "", "Utilize": "ইউটিলাইজ", "Valid time units:": "সময়ের গ্রহণযোগ্য এককসমূহ:", "Valves": "", "Valves updated": "", "Valves updated successfully": "", "variable": "ভেরিয়েবল", "variable to have them replaced with clipboard content.": "ক্লিপবোর্ডের কন্টেন্ট দিয়ে যেই ভেরিয়েবল রিপ্লেস করা যাবে।", "Verify Connection": "", "Verify SSL Certificate": "", "Version": "ভার্সন", "Version {{selectedVersion}} of {{totalVersions}}": "", "View Replies": "", "View Result from **{{NAME}}**": "", "Visibility": "", "Vision": "", "Voice": "", "Voice Input": "", "Voice mode": "", "Warning": "সতর্কীকরণ", "Warning:": "", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "সতর্কীকরণ: আপনি যদি আপনার এম্বেডিং মডেল আপডেট বা পরিবর্তন করেন, তাহলে আপনাকে সমস্ত নথি পুনরায় আমদানি করতে হবে।.", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "", "Web": "ওয়েব", "Web API": "", "Web Loader Engine": "", "Web Search": "ওয়েব অনুসন্ধান", "Web Search Engine": "ওয়েব সার্চ ইঞ্জিন", "Web Search in Chat": "", "Web Search Query Generation": "", "Webhook URL": "ওয়েবহুক URL", "WebUI Settings": "WebUI সেটিংসমূহ", "WebUI URL": "", "WebUI will make requests to \"{{url}}\"": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "Weight of BM25 Retrieval": "", "What are you trying to achieve?": "", "What are you working on?": "", "What's New in": "এতে নতুন কী", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "", "Whisper (Local)": "", "Why?": "", "Widescreen Mode": "", "Won": "", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "", "Workspace": "ওয়ার্কস্পেস", "Workspace Permissions": "", "Write": "", "Write a prompt suggestion (e.g. Who are you?)": "একটি প্রম্পট সাজেশন লিখুন (যেমন Who are you?)", "Write a summary in 50 words that summarizes [topic or keyword].": "৫০ শব্দের মধ্যে [topic or keyword] এর একটি সারসংক্ষেপ লিখুন।", "Write something...": "", "Yacy Instance URL": "", "Yacy Password": "", "Yacy Username": "", "Yesterday": "আ<PERSON><PERSON><PERSON><PERSON>", "You": "আপনি", "You are currently using a trial license. Please contact support to upgrade your license.": "", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "", "You cannot upload an empty file.": "", "You do not have permission to upload files.": "", "You have no archived conversations.": "আপনার কোনও আর্কাইভ করা কথোপকথন নেই।", "You have shared this chat": "আপনি এই চ্যাটটি শেয়ার করেছেন", "You're a helpful assistant.": "আপনি একজন উপকারী এসিস্ট্যান্ট", "You're now logged in.": "আপনি এখন লগইন করা অবস্থায় আছেন", "Your account status is currently pending activation.": "", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "", "Youtube": "YouTube", "Youtube Language": "", "Youtube Proxy URL": ""}
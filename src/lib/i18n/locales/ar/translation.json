{"-1 for no limit, or a positive integer for a specific limit": "-1 لعدم وجود حد، أو عدد صحيح موجب لحد معين", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "الحروف 's'، 'm'، 'h'، 'd'، 'w' أو '-1' لعدم انتهاء الصلاحية.", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(مثال: `sh webui.sh --api --api-auth اسم_المستخدم_كلمة_المرور`)", "(e.g. `sh webui.sh --api`)": "(مثال: تشغيل الأمر: `sh webui.sh --api`)", "(latest)": "(<PERSON><PERSON><PERSON><PERSON>)", "(leave blank for to use commercial endpoint)": "", "[Last] dddd [at] h:mm A": "", "[Today at] h:mm A": "", "[Yesterday at] h:mm A": "", "{{ models }}": "النماذج: {{ models }}", "{{COUNT}} Available Tools": "", "{{COUNT}} characters": "", "{{COUNT}} hidden lines": "{{COUNT}} سطر/أسطر مخفية", "{{COUNT}} Replies": "{{COUNT}} رد/ردود", "{{COUNT}} words": "", "{{user}}'s Chats": "محادثات المستخدم {{user}}", "{{webUIName}} Backend Required": "يتطلب الخلفية الخاصة بـ {{webUIName}}", "*Prompt node ID(s) are required for image generation": "*معرّف/معرّفات عقدة الموجه مطلوبة لتوليد الصور", "A new version (v{{LATEST_VERSION}}) is now available.": "يتوفر الآن إصدار جديد (v{{LATEST_VERSION}}).", "A task model is used when performing tasks such as generating titles for chats and web search queries": "يُستخدم نموذج المهام عند تنفيذ مهام مثل توليد عناوين المحادثات واستعلامات البحث على الويب", "a user": "مستخدم", "About": "حو<PERSON>", "Accept autocomplete generation / Jump to prompt variable": "قبول توليد الإكمال التلقائي / الانتقال إلى متغير الموجه", "Access": "الوصول", "Access Control": "التحكم في الوصول", "Accessible to all users": "متاح لجميع المستخدمين", "Account": "الحساب", "Account Activation Pending": "انتظار تفعيل الحساب", "Accurate information": "معلومات دقيقة", "Action": "", "Actions": "الإجراءات", "Activate": "تفعيل", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "قم بتفعيل هذا الأمر بكتابة \"/{{COMMAND}}\" في مدخل المحادثة.", "Active Users": "المستخدمون النشطون", "Add": "إضافة", "Add a model ID": "إضافة معرّف نموذج", "Add a short description about what this model does": "أضف وصفًا قصيرًا لما يفعله هذا النموذج", "Add a tag": "أض<PERSON> وسم", "Add Arena Model": "إضافة نموذج الساحة", "Add Connection": "إضافة اتصال", "Add Content": "إضافة محتوى", "Add content here": "أ<PERSON><PERSON> المحتوى هنا", "Add Custom Parameter": "", "Add custom prompt": "إضافة موجه مخصص", "Add Files": "إضافة ملفات", "Add Group": "إضافة مجموعة", "Add Memory": "إضافة ذاكرة", "Add Model": "إضافة نموذج", "Add Reaction": "إضافة تفاعل", "Add Tag": "إضافة وسم", "Add Tags": "إضافة وسوم", "Add text content": "إضافة محتوى نصي", "Add User": "إضافة مستخدم", "Add User Group": "إضافة مجموعة مستخدمين", "Adjusting these settings will apply changes universally to all users.": "تعديل هذه الإعدادات سيطبق التغييرات على جميع المستخدمين بشكل عام.", "admin": "المسؤول", "Admin": "المسؤول", "Admin Panel": "لوحة المسؤول", "Admin Settings": "إعدادات المسؤول", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "للمسؤولين الوصول إلى جميع الأدوات في جميع الأوقات؛ بينما يحتاج المستخدمون إلى تعيين أدوات لكل نموذج في مساحة العمل.", "Advanced Parameters": "المعلمات المتقدمة", "Advanced Params": "المعلمات المتقدمة", "AI": "", "All": "الكل", "All Documents": "جميع المستندات", "All models deleted successfully": "تم حذف جميع النماذج بنجاح", "Allow Call": "", "Allow Chat Controls": "السماح بوسائل التحكم في المحادثة", "Allow Chat Delete": "السماح بحذ<PERSON> المحادثة", "Allow Chat Deletion": "السماح بحذ<PERSON> المحادثة", "Allow Chat Edit": "السماح بتعديل المحادثة", "Allow Chat Export": "", "Allow Chat Share": "", "Allow Chat System Prompt": "", "Allow File Upload": "السماح بتحميل الملفات", "Allow Multiple Models in Chat": "", "Allow non-local voices": "السماح بالأصوات غير المحلية", "Allow Speech to Text": "", "Allow Temporary Chat": "السماح بالمحادثة المؤقتة", "Allow Text to Speech": "", "Allow User Location": "السماح بتحديد موقع المستخدم", "Allow Voice Interruption in Call": "السماح بانقطاع الصوت أثناء المكالمة", "Allowed Endpoints": "النقاط النهائية المسموح بها", "Allowed File Extensions": "", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "", "Already have an account?": "هل لديك حساب بالفعل؟", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "بديل للـ top_p، ويهدف إلى ضمان توازن بين الجودة والتنوع. تمثل المعلمة p الحد الأدنى لاحتمالية اعتبار الرمز مقارنة باحتمالية الرمز الأكثر احتمالاً. على سبيل المثال، مع p=0.05 والرمز الأكثر احتمالاً لديه احتمال 0.9، يتم ترشيح القيم الأقل من 0.045.", "Always": "دائمًا", "Always Collapse Code Blocks": "", "Always Expand Details": "", "Always Play Notification Sound": "", "Amazing": "رائع", "an assistant": "مسا<PERSON>د", "Analyzed": "تم التحليل", "Analyzing...": "جارٍ التحليل...", "and": "و", "and {{COUNT}} more": "و{{COUNT}} المزيد", "and create a new shared link.": "وإنشاء رابط مشترك جديد.", "Android": "", "API": "", "API Base URL": "الرابط الأساسي لواجهة API", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "", "API Key": "مفتاح واجهة برمجة التطبيقات (API)", "API Key created.": "تم إنشاء مفتاح واجهة API.", "API Key Endpoint Restrictions": "قيود نقاط نهاية مفتاح API", "API keys": "مفاتيح واجهة برمجة التطبيقات (API)", "API Version": "", "Application DN": "DN التطبيق", "Application DN Password": "كلمة مرور DN التطبيق", "applies to all users with the \"user\" role": "ينطبق على جميع المستخدمين الذين لديهم دور \"مستخدم\"", "April": "أب<PERSON>يل", "Archive": "أرشيف", "Archive All Chats": "أرشفة جميع المحادثات", "Archived Chats": "المحادثات المؤرشفة", "archived-chat-export": "تصدير المحادثات المؤرشفة", "Are you sure you want to clear all memories? This action cannot be undone.": "هل أنت متأكد من رغبتك في مسح جميع الذكريات؟ لا يمكن التراجع عن هذا الإجراء.", "Are you sure you want to delete this channel?": "هل أنت متأكد من رغبتك في حذف هذه القناة؟", "Are you sure you want to delete this message?": "هل أنت متأكد من رغبتك في حذف هذه الرسالة؟", "Are you sure you want to unarchive all archived chats?": "هل أنت متأكد من رغبتك في إلغاء أرشفة جميع المحادثات المؤرشفة؟", "Are you sure?": "هل أنت متأكد؟", "Arena Models": "نماذج الساحة", "Artifacts": "القطع الأثرية", "Ask": "اسأل", "Ask a question": "اطرح سؤالاً", "Assistant": "المساعد", "Attach file from knowledge": "إرفاق ملف من المعرفة", "Attention to detail": "الاهتمام بالتفاصيل", "Attribute for Mail": "خاصية للبريد", "Attribute for Username": "خاصية لاسم المستخدم", "Audio": "الصوت", "August": "أغسطس", "Auth": "", "Authenticate": "توثيق", "Authentication": "المصادقة", "Auto": "", "Auto-Copy Response to Clipboard": "نسخ الرد تلقائيًا إلى الحافظة", "Auto-playback response": "تشغيل الرد تلقائيًا", "Autocomplete Generation": "توليد الإكمال التلقائي", "Autocomplete Generation Input Max Length": "الح<PERSON> الأقصى لطول مدخل توليد الإكمال التلقائي", "Automatic1111": "Automatic1111 (أوتوماتيك 1111)", "AUTOMATIC1111 Api Auth String": "سلسلة توثيق API لـ AUTOMATIC1111", "AUTOMATIC1111 Base URL": "الرابط الأساسي لـ AUTOMATIC1111", "AUTOMATIC1111 Base URL is required.": "الرابط الأساسي لـ AUTOMATIC1111 مطلوب.", "Available list": "القائمة المتاحة", "Available Tools": "", "available!": "متاح!", "Awful": "فظيع", "Azure AI Speech": "نطق Azure AI (مايكروسوفت)", "Azure Region": "منطقة Azure", "Back": "عودة", "Bad Response": "رد سيئ", "Banners": "لافتات", "Base Model (From)": "النموذج الأساسي (من)", "Base Model List Cache speeds up access by fetching base models only at startup or on settings save—faster, but may not show recent base model changes.": "", "before": "قبل", "Being lazy": "كونك كسولاً", "Beta": "بيتا", "Bing Search V7 Endpoint": "نقطة نهاية Bing Search V7", "Bing Search V7 Subscription Key": "مفتا<PERSON> اشتراك Bing Search V7", "Bocha Search API Key": "مفتاح API لـ Bocha Search", "Bold": "", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "تعزيز أو معاقبة رموز محددة لردود مقيدة. ستتراوح قيم التحيز بين -100 و100 (شاملة). (افتراضي: لا شيء)", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "", "Brave Search API Key": "مفتاح API لـ Brave Search", "Bullet List": "", "By {{name}}": "بواسطة {{name}}", "Bypass Embedding and Retrieval": "تجاوز التضمين والاسترجاع", "Bypass Web Loader": "", "Cache Base Model List": "", "Calendar": "التقويم", "Call": "مكالمة", "Call feature is not supported when using Web STT engine": "ميزة الاتصال غير مدعومة عند استخدام محرك Web STT", "Camera": "الكاميرا", "Cancel": "إلغاء", "Capabilities": "القدرات", "Capture": "التقاط", "Capture Audio": "", "Certificate Path": "مسار الشهادة", "Change Password": "تغيير كلمة المرور", "Channel Name": "اسم القناة", "Channels": "القنوات", "Character": "الشخصية", "Character limit for autocomplete generation input": "حد الأحر<PERSON> لم<PERSON><PERSON><PERSON> توليد الإكمال التلقائي", "Chart new frontiers": "رسم آفاق جديدة", "Chat": "محاد<PERSON>ة", "Chat Background Image": "صورة خلفية المحادثة", "Chat Bubble UI": "واجهة فقاعات المحادثة", "Chat Controls": "ضوابط المحادثة", "Chat direction": "اتجاه المحادثة", "Chat Overview": "نظرة عامة على المحادثة", "Chat Permissions": "أذونات المحادثة", "Chat Tags Auto-Generation": "الإنشاء التلقائي لطاچ المحادثة", "Chats": "المحادثات", "Check Again": "تحقق مرة أخرى", "Check for updates": "تحقق من التحديثات", "Checking for updates...": "جارٍ التحقق من التحديثات...", "Choose a model before saving...": "اختر نموذجًا قبل الحفظ...", "Chunk Overlap": "تداخل القطع", "Chunk Size": "حجم القطعة", "Ciphers": "التشفيرات", "Citation": "اقتباس", "Citations": "", "Clear memory": "م<PERSON><PERSON> الذاكرة", "Clear Memory": "م<PERSON><PERSON> الذاكرة", "click here": "انقر هنا", "Click here for filter guides.": "انقر هنا للحصول على أدلة الفلاتر.", "Click here for help.": "انقر هنا للمساعدة.", "Click here to": "انقر هنا لـ", "Click here to download user import template file.": "انقر هنا لتنزيل ملف قالب استيراد المستخدم.", "Click here to learn more about faster-whisper and see the available models.": "انقر هنا لمعرفة المزيد عن faster-whisper ورؤية النماذج المتاحة.", "Click here to see available models.": "انقر هنا لرؤية النماذج المتاحة.", "Click here to select": "انقر هنا للاختيار", "Click here to select a csv file.": "انقر هنا لاختيار ملف CSV.", "Click here to select a py file.": "انقر هنا لاختيار ملف PY.", "Click here to upload a workflow.json file.": "انقر هنا لتحميل ملف workflow.json.", "click here.": "انقر هنا.", "Click on the user role button to change a user's role.": "انقر على زر دور المستخدم لتغيير دور المستخدم.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "تم رفض إذن الكتابة إلى الحافظة. يرجى التحقق من إعدادات المتصفح لمنح الوصول اللازم.", "Clone": "استنساخ", "Clone Chat": "استنساخ المحادثة", "Clone of {{TITLE}}": "استنساخ لـ {{TITLE}}", "Close": "إغلاق", "Close Configure Connection Modal": "", "Close modal": "", "Close settings modal": "", "Code Block": "", "Code execution": "تنفيذ الشيفرة", "Code Execution": "تنفيذ الشيفرة", "Code Execution Engine": "محرك تنفيذ الشيفرة", "Code Execution Timeout": "مهلة تنفيذ الشيفرة", "Code formatted successfully": "تم تنسيق الشيفرة بنجاح", "Code Interpreter": "مفسر الشيفرة", "Code Interpreter Engine": "محرك مفسر الشيفرة", "Code Interpreter Prompt Template": "قالب موجه مفسر الشيفرة", "Collapse": "طي", "Collection": "المجموعة", "Color": "اللون", "ComfyUI": "ComfyUI", "ComfyUI API Key": "مفتاح API لـ ComfyUI", "ComfyUI Base URL": "عنوان الأساس لـ ComfyUI", "ComfyUI Base URL is required.": "عنوان الأساس لـ ComfyUI مطلوب.", "ComfyUI Workflow": "سير ع<PERSON>ل ComfyUI", "ComfyUI Workflow Nodes": "عقد سير ع<PERSON><PERSON> Comfy<PERSON>", "Command": "الأمر", "Comment": "", "Completions": "الإكمالات", "Concurrent Requests": "الطلبات المتزامنة", "Configure": "تكوين", "Confirm": "تأكيد", "Confirm Password": "تأكيد كلمة المرور", "Confirm your action": "<PERSON><PERSON><PERSON>جراءك", "Confirm your new password": "أكد كلمة مرورك الجديدة", "Connect to your own OpenAI compatible API endpoints.": "اتصل بنقاط نهاية API المتوافقة مع OpenAI الخاصة بك.", "Connect to your own OpenAPI compatible external tool servers.": "", "Connection failed": "", "Connection successful": "", "Connection Type": "", "Connections": "الاتصالات", "Connections saved successfully": "", "Connections settings updated": "", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "يقيّد الجهد في التفكير لنماذج التفكير. ينطبق فقط على نماذج التفكير من مقدمي خدمات محددين يدعمون جهد التفكير.", "Contact Admin for WebUI Access": "اتصل بالمسؤول للوصول إلى واجهة الويب", "Content": "المحتوى", "Content Extraction Engine": "محرك استخراج المحتوى", "Continue Response": "متابعة الرد", "Continue with {{provider}}": "متابعة مع {{provider}}", "Continue with Email": "متابعة باستخدام البريد الإلكتروني", "Continue with LDAP": "متابعة باستخدام LDAP", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "تحكم في كيفية تقسيم نص الرسالة لطلبات تحويل النص إلى كلام. 'علامات الترقيم' تقسم إلى جمل، 'الفقرات' تقسم إلى فقرات، و'لا شيء' يحتفظ بالرسالة كسلسلة واحدة.", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "تحكم في تكرار تسلسلات الرموز في النص المولد. قيمة أعلى (مثال: 1.5) ستعاقب التكرارات بشدة أكبر، بينما قيمة أقل (مثال: 1.1) ستكون أكثر تسامحاً. عند القيمة 1، يتم تعطيله.", "Controls": "الضوابط", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "يتحكم في التوازن بين الترابط والتنوع في الناتج. قيمة أقل ستؤدي إلى نص أكثر تركيزاً وترابطاً.", "Copied": "تم النسخ", "Copied link to clipboard": "", "Copied shared chat URL to clipboard!": "تم نسخ رابط المحادثة المشترك إلى الحافظة!", "Copied to clipboard": "تم النسخ إلى الحافظة", "Copy": "نسخ", "Copy Formatted Text": "", "Copy last code block": "نسخ آخر كتلة شيفرة", "Copy last response": "نسخ آخر رد", "Copy link": "", "Copy Link": "نسخ الرابط", "Copy to clipboard": "نسخ إلى الحافظة", "Copying to clipboard was successful!": "تم النسخ إلى الحافظة بنجاح!", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "يجب أن يتم تكوين CORS بشكل صحيح من قبل المزود للسماح بالطلبات من Open WebUI.", "Create": "إنشاء", "Create a knowledge base": "إنشاء قاعدة معرفة", "Create a model": "إنشاء نموذج", "Create Account": "إنشاء حساب", "Create Admin Account": "إنشاء حساب مسؤول", "Create Channel": "إنشاء قناة", "Create Group": "إنشاء مجموعة", "Create Knowledge": "إنشاء معرفة", "Create new key": "إنشاء مفتاح جديد", "Create new secret key": "إنشاء مفتاح سري جديد", "Create Note": "", "Create your first note by clicking on the plus button below.": "", "Created at": "تم الإنشاء في", "Created At": "تاريخ الإنشاء", "Created by": "تم الإنشاء بواسطة", "CSV Import": "استيراد CSV", "Ctrl+Enter to Send": "اضغط Ctrl+Enter للإرسال", "Current Model": "النموذج الحالي", "Current Password": "كلمة المرور الحالية", "Custom": "مخصص", "Custom description enabled": "", "Custom Parameter Name": "", "Custom Parameter Value": "", "Danger Zone": "منطقة الخطر", "Dark": "دا<PERSON>ن", "Database": "قاعدة البيانات", "Datalab Marker API": "", "Datalab Marker API Key required.": "", "DD/MM/YYYY": "", "December": "ديسمبر", "Default": "افتراضي", "Default (Open AI)": "افتراضي (Open AI)", "Default (SentenceTransformers)": "افتراضي (SentenceTransformers)", "Default description enabled": "", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "الوضع الافتراضي يعمل مع مجموعة أوسع من النماذج من خلال استدعاء الأدوات مرة واحدة قبل التنفيذ. أما الوضع الأصلي فيستخدم قدرات استدعاء الأدوات المدمجة في النموذج، لكنه يتطلب دعمًا داخليًا لهذه الميزة.", "Default Model": "النموذج الافتراضي", "Default model updated": "الإفتراضي تحديث الموديل", "Default Models": "النماذج الافتراضية", "Default permissions": "الأذونات الافتراضية", "Default permissions updated successfully": "تم تحديث الأذونات الافتراضية بنجاح", "Default Prompt Suggestions": "الإفتراضي Prompt الاقتراحات", "Default to 389 or 636 if TLS is enabled": "الافتراضي هو 389 أو 636 إذا تم تمكين TLS", "Default to ALL": "الافتراضي هو الكل", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "", "Default User Role": "الإفتراضي صلاحيات المستخدم", "Delete": "<PERSON><PERSON><PERSON>", "Delete a model": "<PERSON><PERSON><PERSON> الموديل", "Delete All Chats": "حذ<PERSON> جميع الدردشات", "Delete All Models": "حذ<PERSON> جميع النماذج", "Delete chat": "<PERSON><PERSON><PERSON> المح<PERSON><PERSON>ه", "Delete Chat": "حذ<PERSON> المحاد<PERSON>ه.", "Delete chat?": "هل تريد حذف المحادثة؟", "Delete folder?": "هل تريد حذف المجلد؟", "Delete function?": "هل تريد حذف الوظيفة؟", "Delete Message": "<PERSON>ذ<PERSON> الرسالة", "Delete message?": "هل تريد حذف الرسالة؟", "Delete note?": "", "Delete prompt?": "هل تريد حذف الموجه؟", "delete this link": "أحذ<PERSON> هذا الرابط", "Delete tool?": "هل تريد حذف الأداة؟", "Delete User": "<PERSON>ذ<PERSON> المستخدم", "Deleted {{deleteModelTag}}": "{{deleteModelTag}} حذف", "Deleted {{name}}": "حذ<PERSON> {{name}}", "Deleted User": "مستخدم محذوف", "Deployment names are required for Azure OpenAI": "", "Describe Pictures in Documents": "", "Describe your knowledge base and objectives": "صف قاعدة معرفتك وأهدافك", "Description": "وصف", "Detect Artifacts Automatically": "", "Dictate": "", "Didn't fully follow instructions": "لم أتبع التعليمات بشكل كامل", "Direct": "", "Direct Connections": "الاتصالات المباشرة", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "تتيح الاتصالات المباشرة للمستخدمين الاتصال بنقاط نهاية API متوافقة مع OpenAI الخاصة بهم.", "Direct Tool Servers": "", "Disable Code Interpreter": "", "Disable Image Extraction": "", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "", "Disabled": "معطّل", "Discover a function": "اكتشف وظيفة", "Discover a model": "اكتشف نموذجا", "Discover a prompt": "اكتشاف موجه", "Discover a tool": "اكتشف أداة", "Discover how to use Open WebUI and seek support from the community.": "اكتشف كيفية استخدام Open WebUI واطلب الدعم من المجتمع.", "Discover wonders": "اكتشف العجائب", "Discover, download, and explore custom functions": "اكتشف، حمّل، واستعرض الوظائف المخصصة", "Discover, download, and explore custom prompts": "اكتشاف وتنزيل واستكشاف المطالبات المخصصة", "Discover, download, and explore custom tools": "اكتشف، حمّل، واستعرض الأدوات المخصصة", "Discover, download, and explore model presets": "اكتشاف وتنزيل واستكشاف الإعدادات المسبقة للنموذج", "Display": "العرض", "Display Emoji in Call": "عرض الرموز التعبيرية أثناء المكالمة", "Display the username instead of You in the Chat": "اعرض اسم المستخدم بدلاً منك في الدردشة", "Displays citations in the response": "عرض المراجع في الرد", "Dive into knowledge": "انغمس في المعرفة", "Do not install functions from sources you do not fully trust.": "لا تقم بتثبيت الوظائف من مصادر لا تثق بها تمامًا.", "Do not install tools from sources you do not fully trust.": "لا تقم بتثبيت الأدوات من مصادر لا تثق بها تمامًا.", "Docling": "", "Docling Server URL required.": "", "Document": "المستند", "Document Intelligence": "تحليل المستندات الذكي", "Document Intelligence endpoint and key required.": "يتطلب نقطة نهاية ومفتاح لتحليل المستندات.", "Documentation": "التوثيق", "Documents": "مستندات", "does not make any external connections, and your data stays securely on your locally hosted server.": "لا يجري أي اتصالات خارجية، وتظل بياناتك آمنة على الخادم المستضاف محليًا.", "Domain Filter List": "قائمة تصفية النطاقات", "Don't have an account?": "ليس لديك حساب؟", "don't install random functions from sources you don't trust.": "لا تقم بتثبيت وظائف عشوائية من مصادر غير موثوقة.", "don't install random tools from sources you don't trust.": "لا تقم بتثبيت أدوات عشوائية من مصادر غير موثوقة.", "Don't like the style": "لا أحب النمط", "Done": "تم", "Download": "تحميل", "Download as SVG": "تنزيل بصيغة SVG", "Download canceled": "تم اللغاء التحميل", "Download Database": "تحميل قاعدة البيانات", "Drag and drop a file to upload or select a file to view": "اسحب الملف وأفلته للرفع أو اختر ملفًا للعرض", "Draw": "ارسم", "Drop any files here to upload": "", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "e.g. '30s','10m'. الوحدات الزمنية الصالحة هي 's', 'm', 'h'.", "e.g. \"json\" or a JSON schema": "", "e.g. 60": "مثال: 60", "e.g. A filter to remove profanity from text": "مثال: مرش<PERSON> لإزالة الألفاظ النابية من النص", "e.g. en": "", "e.g. My Filter": "مثال: مرشحي", "e.g. My Tools": "مثال: أدواتي", "e.g. my_filter": "مثال: my_filter", "e.g. my_tools": "مثال: my_tools", "e.g. pdf, docx, txt": "", "e.g. Tools for performing various operations": "مثال: أدوات لتنفيذ عمليات متنوعة", "e.g., 3, 4, 5 (leave blank for default)": "", "e.g., audio/wav,audio/mpeg,video/* (leave blank for defaults, * for all)": "", "e.g., en-US,ja-JP (leave blank for auto-detect)": "", "e.g., westus (leave blank for eastus)": "", "e.g.) en,fr,de": "", "Edit": "تعديل", "Edit Arena Model": "تعديل نموذج Arena", "Edit Channel": "تعديل القناة", "Edit Connection": "تعديل الاتصال", "Edit Default Permissions": "تعديل الأذونات الافتراضية", "Edit Folder": "", "Edit Memory": "تعديل الذاكرة", "Edit User": "تعديل المستخدم", "Edit User Group": "تعديل مجموعة المستخدمين", "Edited": "", "Editing": "", "Eject": "", "ElevenLabs": "ElevenLabs", "Email": "البريد", "Embark on adventures": "انطلق في مغامرات", "Embedding": "تضمين", "Embedding Batch Size": "حجم دفعة التضمين", "Embedding Model": "نموذج التضمين", "Embedding Model Engine": "تضمين محرك النموذج", "Embedding model set to \"{{embedding_model}}\"": "تم تعيين نموذج التضمين على \"{{embedding_model}}\"", "Enable API Key": "تفعيل مفتاح API", "Enable autocomplete generation for chat messages": "تفعيل توليد الإكمال التلقائي لرسائل الدردشة", "Enable Code Execution": "تفعيل تنفيذ الكود", "Enable Code Interpreter": "تفعيل مفسر الكود", "Enable Community Sharing": "تمكين مشاركة المجتمع", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "تفعيل قفل الذاكرة (mlock) لمنع إخراج بيانات النموذج من الذاكرة. يساعد هذا الخيار في الحفاظ على الأداء من خلال منع حدوث أخطاء في الوصول وضمان سرعة الوصول إلى البيانات.", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "تفعيل تعيين الذاكرة (mmap) لتحميل بيانات النموذج. يسمح هذا الخيار للنظام باستخدام التخزين كامتداد للذاكرة RAM عن طريق معاملة ملفات القرص كما لو كانت في RAM. قد يحسن أداء النموذج، لكن قد لا يعمل بشكل صحيح مع جميع الأنظمة وقد يستهلك مساحة كبيرة من القرص.", "Enable Message Rating": "تفعيل تقييم الرسائل", "Enable Mirostat sampling for controlling perplexity.": "تفعيل أخذ عينات Mirostat للتحكم في درجة التعقيد.", "Enable New Sign Ups": "تفعيل عمليات التسجيل الجديدة", "Enabled": "م<PERSON>عل", "Endpoint URL": "", "Enforce Temporary Chat": "", "Enhance": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "تأكد من أن ملف CSV الخاص بك يتضمن 4 أعمدة بهذا الترتيب: Name, Email, Password, Role.", "Enter {{role}} message here": "أدخل رسالة {{role}} هنا", "Enter a detail about yourself for your LLMs to recall": "ادخل معلومات عنك تريد أن يتذكرها الموديل", "Enter a title for the pending user info overlay. Leave empty for default.": "", "Enter a watermark for the response. Leave empty for none.": "", "Enter api auth string (e.g. username:password)": "أدخل سلسلة توثيق API (مثال: username:password)", "Enter Application DN": "أدخل DN التطبيق", "Enter Application DN Password": "أدخل كلمة مرور DN التطبيق", "Enter Bing Search V7 Endpoint": "أدخل نقطة نهاية Bing Search V7", "Enter Bing Search V7 Subscription Key": "أدخل مفتاح اشتراك Bing Search V7", "Enter BM25 Weight": "", "Enter Bocha Search API Key": "أدخل مفتاح API لـ Bocha Search", "Enter Brave Search API Key": "أدخل مفتاح واجهة برمجة تطبيقات البحث الشجاع", "Enter certificate path": "أدخل مسار الشهادة", "Enter CFG Scale (e.g. 7.0)": "أدخل مقياس CFG (مثال: 7.0)", "Enter Chunk Overlap": "أ<PERSON><PERSON><PERSON> ال<PERSON><PERSON><PERSON> Overlap", "Enter Chunk Size": "أ<PERSON><PERSON><PERSON>k الحجم", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "أدخل أزواج \"الرمز:قيمة التحيز\" مفصولة بفواصل (مثال: 5432:100، 413:-100)", "Enter Config in JSON format": "", "Enter content for the pending user info overlay. Leave empty for default.": "", "Enter Datalab Marker API Key": "", "Enter description": "أد<PERSON>ل الوصف", "Enter Docling OCR Engine": "", "Enter Docling OCR Language(s)": "", "Enter Docling Server URL": "", "Enter Document Intelligence Endpoint": "أدخل نقطة نهاية تحليل المستندات", "Enter Document Intelligence Key": "أدخل مفتاح تحليل المستندات", "Enter domains separated by commas (e.g., example.com,site.org)": "أدخل النطاقات مفصولة بفواصل (مثال: example.com,site.org)", "Enter Exa API Key": "أدخل مفتاح API لـ Exa", "Enter External Document Loader API Key": "", "Enter External Document Loader URL": "", "Enter External Web Loader API Key": "", "Enter External Web Loader URL": "", "Enter External Web Search API Key": "", "Enter External Web Search URL": "", "Enter Firecrawl API Base URL": "", "Enter Firecrawl API Key": "", "Enter folder name": "", "Enter Github Raw URL": "أدخل عنوان URL ل Github Raw", "Enter Google PSE API Key": "أدخل مفتاح واجهة برمجة تطبيقات PSE من Google", "Enter Google PSE Engine Id": "أدخل معرف محرك PSE من Google", "Enter Image Size (e.g. 512x512)": "(e.g. 512x512) أدخل حجم الصورة ", "Enter Jina API Key": "أدخل مفتاح API لـ Jina", "Enter Jupyter Password": "أد<PERSON>ل كلمة مرور Ju<PERSON>ter", "Enter Jupyter Token": "<PERSON><PERSON><PERSON><PERSON> ر<PERSON><PERSON>", "Enter Jupyter URL": "أ<PERSON><PERSON><PERSON> عنوان <PERSON>", "Enter Kagi Search API Key": "أدخل مفتاح API لـ Kagi Search", "Enter Key Behavior": "أدخل سلوك المفتاح", "Enter language codes": "أ<PERSON><PERSON><PERSON> كود اللغة", "Enter Mistral API Key": "", "Enter Model ID": "أدخل معرف النموذج", "Enter model tag (e.g. {{modelTag}})": "(e.g. {{modelTag}}) أدخل الموديل تاق", "Enter Mojeek Search API Key": "أدخل مفتاح API لـ Mojeek Search", "Enter name": "", "Enter New Password": "", "Enter Number of Steps (e.g. 50)": "(e.g. 50) أد<PERSON><PERSON> عدد الخطوات", "Enter Perplexity API Key": "أدخل مفتاح API لـ Perplexity", "Enter Playwright Timeout": "", "Enter Playwright WebSocket URL": "", "Enter proxy URL (e.g. **************************:port)": "أدخل عنوان البروكسي (مثال: **************************:port)", "Enter reasoning effort": "أد<PERSON>ل مستوى الجهد في الاستدلال", "Enter Sampler (e.g. Euler a)": "أدخل العينة (مثال: Euler a)", "Enter Scheduler (e.g. Karras)": "أد<PERSON>ل المجدول (مثال: Karras)", "Enter Score": "أد<PERSON>ل النتيجة", "Enter SearchApi API Key": "أدخل مفتاح API لـ SearchApi", "Enter SearchApi Engine": "أد<PERSON>ل محرك SearchApi", "Enter Searxng Query URL": "أدخل عنوان URL لاستعلام Searxng", "Enter Seed": "أدخل القيمة الابتدائية (Seed)", "Enter SerpApi API Key": "أدخل مفتاح API لـ SerpApi", "Enter SerpApi Engine": "أدخل محرك SerpApi", "Enter Serper API Key": "أدخل مفتاح واجهة برمجة تطبيقات <PERSON>per", "Enter Serply API Key": "أدخل مفتاح API لـ Serply", "Enter Serpstack API Key": "أدخل مفتاح واجهة برمجة تطبيقات Serpstack", "Enter server host": "أدخل مضيف الخادم", "Enter server label": "أدخل تسمية الخادم", "Enter server port": "أدخل منفذ الخادم", "Enter Sougou Search API sID": "", "Enter Sougou Search API SK": "", "Enter stop sequence": "أدخل تسلسل التوقف", "Enter system prompt": "أدخل موجه النظام", "Enter system prompt here": "", "Enter Tavily API Key": "أدخل مفتاح API لـ Tavily", "Enter Tavily Extract Depth": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "أدخل الرابط العلني لـ WebUI الخاص بك. سيتم استخدام هذا الرابط لإنشاء روابط داخل الإشعارات.", "Enter the URL of the function to import": "", "Enter the URL to import": "", "Enter Tika Server URL": "أدخل رابط خادم Tika", "Enter timeout in seconds": "أد<PERSON>ل المهلة بالثواني", "Enter to Send": "اضغط Enter للإرسال", "Enter Top K": "أدخل Top K", "Enter Top K Reranker": "", "Enter URL (e.g. http://127.0.0.1:7860/)": "الرابط (e.g. http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "URL (e.g. http://localhost:11434)", "Enter Yacy Password": "", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "", "Enter Yacy Username": "", "Enter your current password": "أدخل كلمة المرور الحالية", "Enter Your Email": "أد<PERSON><PERSON> البريد الاكتروني", "Enter Your Full Name": "أد<PERSON>ل الاسم كامل", "Enter your message": "أدخل رسالتك", "Enter your name": "", "Enter Your Name": "", "Enter your new password": "أدخل كلمة المرور الجديدة", "Enter Your Password": "ادخل كلمة المرور", "Enter Your Role": "أد<PERSON>ل الصلاحيات", "Enter Your Username": "أدخل اسم المستخدم الخاص بك", "Enter your webhook URL": "أدخل رابط Webhook الخاص بك", "Error": "خطأ", "ERROR": "خطأ", "Error accessing Google Drive: {{error}}": "حد<PERSON> خطأ أثناء الوصول إلى Google Drive: {{error}}", "Error accessing media devices.": "", "Error starting recording.": "", "Error unloading model: {{error}}": "", "Error uploading file: {{error}}": "حد<PERSON> خطأ أثناء تحميل الملف: {{error}}", "Evaluations": "التقييمات", "Everyone": "", "Exa API Key": "مفتاح API لـ Exa", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "مثال: (&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "مثال: ALL", "Example: mail": "مثال: mail", "Example: ou=users,dc=foo,dc=example": "مثال: ou=users,dc=foo,dc=example", "Example: sAMAccountName or uid or userPrincipalName": "مثال: sAMAccountName أو uid أو userPrincipalName", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "تجاوزت عدد المقاعد المسموح بها في الترخيص. يرجى الاتصال بالدعم لزيادتها.", "Exclude": "استبعاد", "Execute code for analysis": "تنفيذ الكود للتحليل", "Executing **{{NAME}}**...": "", "Expand": "توسيع", "Experimental": "تجريبي", "Explain": "شرح", "Explore the cosmos": "استكشف الكون", "Export": "تصدير", "Export All Archived Chats": "تصدير جميع المحادثات المؤرشفة", "Export All Chats (All Users)": "تصدير جميع الدردشات (جميع المستخدمين)", "Export chat (.json)": "تصدير المحادثة (.json)", "Export Chats": "تصدير جميع الدردشات", "Export Config to JSON File": "تصدير الإعدادات إلى ملف JSON", "Export Functions": "تصدير الوظائف", "Export Models": "نماذج التصدير", "Export Presets": "تصدير الإعدادات المسبقة", "Export Prompt Suggestions": "", "Export Prompts": "مطالبات التصدير", "Export to CSV": "تصدير إلى CSV", "Export Tools": "تصدير الأدوات", "External": "", "External Document Loader URL required.": "", "External Task Model": "", "External Web Loader API Key": "", "External Web Loader URL": "", "External Web Search API Key": "", "External Web Search URL": "", "Failed to add file.": "فشل في إضافة الملف.", "Failed to connect to {{URL}} OpenAPI tool server": "", "Failed to copy link": "", "Failed to create API Key.": "فشل في إنشاء مفتاح API.", "Failed to delete note": "", "Failed to extract content from the file: {{error}}": "", "Failed to extract content from the file.": "", "Failed to fetch models": "فشل في جلب النماذج", "Failed to generate title": "", "Failed to load file content.": "", "Failed to read clipboard contents": "فشل في قراءة محتويات الحافظة", "Failed to save connections": "", "Failed to save models configuration": "فشل في حفظ إعدادات النماذج", "Failed to update settings": "فشل في تحديث الإعدادات", "Failed to upload file.": "فشل في رفع الملف.", "Features": "الميزات", "Features Permissions": "أذونات الميزات", "February": "فبراير", "Feedback Details": "", "Feedback History": "سجل الملاحظات", "Feedbacks": "الملاحظات", "Feel free to add specific details": "لا تتردد في إضافة تفاصيل محددة", "File": "مل<PERSON>", "File added successfully.": "تم إضافة الملف بنجاح.", "File content updated successfully.": "تم تحديث محتوى الملف بنجاح.", "File Mode": "وضع الملف", "File not found.": "لم يتم العثور على الملف.", "File removed successfully.": "تم حذف الم<PERSON>ف بنجاح.", "File size should not exceed {{maxSize}} MB.": "يجب ألا يتجاوز حجم الملف {{maxSize}} ميغابايت.", "File Upload": "", "File uploaded successfully": "تم رفع الملف بنجاح", "Files": "الملفات", "Filter": "", "Filter is now globally disabled": "تم الآن تعطيل الفلتر على مستوى النظام", "Filter is now globally enabled": "تم الآن تفعيل الفلتر على مستوى النظام", "Filters": "الفلاتر", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "تم اكتشاف انتحال بصمة الإصبع: غير قادر على استخدام الأحرف الأولى كصورة رمزية. الافتراضي لصورة الملف الشخصي الافتراضية.", "Firecrawl API Base URL": "", "Firecrawl API Key": "", "Fluidly stream large external response chunks": "دفق قطع الاستجابة الخارجية الكبيرة بسلاسة", "Focus chat input": "التركيز على إدخال الدردشة", "Folder deleted successfully": "تم حذف الم<PERSON><PERSON>د بنجاح", "Folder Name": "", "Folder name cannot be empty.": "لا يمكن أن يكون اسم المجلد فارغًا.", "Folder name updated successfully": "تم تحديث اسم المجلد بنجاح", "Folder updated successfully": "", "Follow up": "", "Follow Up Generation": "", "Follow Up Generation Prompt": "", "Follow-Up Auto-Generation": "", "Followed instructions perfectly": "اتبعت التعليمات على أكمل وجه", "Force OCR": "", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "", "Forge new paths": "أنشئ مسارات جديدة", "Form": "نموذج", "Format your variables using brackets like this:": "نسّق متغيراتك باستخدام الأقواس بهذا الشكل:", "Forwards system user session credentials to authenticate": "", "Full Context Mode": "وضع السياق الكامل", "Function": "وظيفة", "Function Calling": "استدعاء الوظائف", "Function created successfully": "تم إنشاء الوظيفة بنجاح", "Function deleted successfully": "تم حذف الوظيفة بنجاح", "Function Description": "وصف الوظيفة", "Function ID": "معرف الوظيفة", "Function imported successfully": "", "Function is now globally disabled": "تم الآن تعطيل الوظيفة على مستوى النظام", "Function is now globally enabled": "تم الآن تفعيل الوظيفة على مستوى النظام", "Function Name": "اسم الوظيفة", "Function updated successfully": "تم تحديث الوظيفة بنجاح", "Functions": "الوظائف", "Functions allow arbitrary code execution.": "الوظائف تتيح تنفيذ كود برمجي مخصص.", "Functions imported successfully": "تم استيراد الوظائف بنجاح", "Gemini": "Gemini", "Gemini API Config": "إعدادات واجهة Gemini API", "Gemini API Key is required.": "مفتاح Gemini API مطلوب.", "General": "عام", "Generate": "", "Generate an image": "توليد صورة", "Generate Image": "توليد صورة", "Generate prompt pair": "توليد زوج من التعليمات", "Generating search query": "إنشاء استعلام بحث", "Generating...": "", "Get information on {{name}} in the UI": "", "Get started": "اب<PERSON><PERSON> الآن", "Get started with {{WEBUI_NAME}}": "ابدأ باستخدام {{WEBUI_NAME}}", "Global": "عام", "Good Response": "استجابة جيدة", "Google Drive": "Google Drive", "Google PSE API Key": "مفتاح واجهة برمجة تطبيقات PSE من Google", "Google PSE Engine Id": "معرف محرك PSE من Google", "Group created successfully": "تم إنشاء المجموعة بنجاح", "Group deleted successfully": "تم حذف المجموعة بنجاح", "Group Description": "وصف المجموعة", "Group Name": "اسم المجموعة", "Group updated successfully": "تم تحديث المجموعة بنجاح", "Groups": "المجموعات", "H1": "", "H2": "", "H3": "", "Haptic Feedback": "الاه<PERSON><PERSON><PERSON><PERSON> اللمسي", "Hello, {{name}}": " {{name}} مرحبا", "Help": "مساعدة", "Help us create the best community leaderboard by sharing your feedback history!": "ساعدنا في إنشاء أفضل قائمة للمتصدرين من خلال مشاركة سجل ملاحظاتك!", "Hex Color": "لون سداسي", "Hex Color - Leave empty for default color": "اللون السداسي - اتركه فارغًا لاستخدام اللون الافتراضي", "Hide": "أخفاء", "Hide from Sidebar": "", "Hide Model": "", "High Contrast Mode": "", "Home": "الصفحة الرئيسية", "Host": "المضيف", "How can I help you today?": "كيف استطيع مساعدتك اليوم؟", "How would you rate this response?": "كيف تقيّم هذا الرد؟", "HTML": "", "Hybrid Search": "البحث الهجين", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "أقر بأنني قرأت وفهمت تبعات هذا الإجراء. أنا على دراية بالمخاطر المرتبطة بتنفيذ كود عشوائي وقد تحققت من موثوقية المصدر.", "ID": "المعرّف", "iframe Sandbox Allow Forms": "", "iframe Sandbox Allow Same Origin": "", "Ignite curiosity": "أشعل الفضول", "Image": "صورة", "Image Compression": "ضغط الصور", "Image Compression Height": "", "Image Compression Width": "", "Image Generation": "توليد الصور", "Image Generation (Experimental)": "توليد الصور (تجريبي)", "Image Generation Engine": "محرك توليد الصور", "Image Max Compression Size": "الح<PERSON> الأقصى لضغط الصورة", "Image Max Compression Size height": "", "Image Max Compression Size width": "", "Image Prompt Generation": "توليد التوجيه للصورة", "Image Prompt Generation Prompt": "نص توجيه توليد الصورة", "Image Settings": "إعدادات الصورة", "Images": "الصور", "Import": "", "Import Chats": "استيراد الدردشات", "Import Config from JSON File": "استيراد الإعدادات من ملف JSON", "Import From Link": "", "Import Functions": "استيراد الوظائف", "Import Models": "استيراد النماذج", "Import Notes": "", "Import Presets": "استيراد الإعدادات المسبقة", "Import Prompt Suggestions": "", "Import Prompts": "مطالبات الاستيراد", "Import Tools": "استيراد الأدوات", "Include": "تضمين", "Include `--api-auth` flag when running stable-diffusion-webui": "أضف الخيار `--api-auth` عند تشغيل stable-diffusion-webui", "Include `--api` flag when running stable-diffusion-webui": "قم بتضمين علامة `-api` عند تشغيل Stable-diffusion-webui", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "يؤثر على سرعة استجابة الخوارزمية للتغذية الراجعة من النص المُولد. معدل تعلم منخفض يؤدي إلى تعديلات أبطأ، بينما معدل أعلى يجعلها أكثر استجابة.", "Info": "معلومات", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "", "Input commands": "إدخال الأوامر", "Input Variables": "", "Insert": "", "Insert Prompt as Rich Text": "", "Install from Github URL": "التثبيت من عنوان URL لجيثب", "Instant Auto-Send After Voice Transcription": "إرسال تلقائي فوري بعد تحويل الصوت إلى نص", "Integration": "التكامل", "Interface": "واجهه المستخدم", "Invalid file content": "", "Invalid file format.": "تنسيق ملف غير صالح.", "Invalid JSON file": "", "Invalid Tag": "تاق غير صالحة", "is typing...": "يكتب...", "Italic": "", "January": "يناير", "Jina API Key": "مفتاح API لـ Jina", "join our Discord for help.": "انضم إلى Discord للحصول على المساعدة.", "JSON": "JSON", "JSON Preview": "معاينة JSON", "July": "يوليو", "June": "يونيو", "Jupyter Auth": "مصاد<PERSON><PERSON> Jupy<PERSON>", "Jupyter URL": "<PERSON><PERSON><PERSON><PERSON>", "JWT Expiration": "JWT تجريبي", "JWT Token": "JWT Token", "Kagi Search API Key": "مفتاح API لـ Kagi Search", "Keep in Sidebar": "", "Key": "المفتاح", "Keyboard shortcuts": "اختصارات لوحة المفاتيح", "Knowledge": "المعرفة", "Knowledge Access": "الوصول إلى المعرفة", "Knowledge Base": "", "Knowledge created successfully.": "تم إنشاء المعرفة بنجاح.", "Knowledge deleted successfully.": "تم حذف المعرفة بنجاح.", "Knowledge Public Sharing": "", "Knowledge reset successfully.": "تم إعادة تعيين المعرفة بنجاح.", "Knowledge updated successfully": "تم تحديث المعرفة بنجاح", "Kokoro.js (Browser)": "Kokoro.js (المتصفح)", "Kokoro.js Dtype": "نوع بيانات Kokoro.js", "Label": "التسمية", "Landing Page Mode": "وضع الصفحة الرئيسية", "Language": "اللغة", "Language Locales": "", "Languages": "", "Last Active": "آخر نشاط", "Last Modified": "آ<PERSON>ر تعديل", "Last reply": "<PERSON><PERSON><PERSON> رد", "LDAP": "LDAP", "LDAP server updated": "تم تحديث خادم LDAP", "Leaderboard": "لوحة المتصدرين", "Learn more about OpenAPI tool servers.": "", "Leave empty for no compression": "", "Leave empty for unlimited": "اتركه فارغًا لعدم وجود حد", "Leave empty to include all models from \"{{url}}\" endpoint": "", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{url}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "اتركه فارغًا لتضمين جميع النماذج أو اختر نماذج محددة", "Leave empty to use the default prompt, or enter a custom prompt": "اتركه فارغًا لاستخدام التوجيه الافتراضي، أو أدخل توجيهًا مخصصًا", "Leave model field empty to use the default model.": "اترك حقل النموذج فارغًا لاستخدام النموذج الافتراضي.", "License": "الترخيص", "Light": "فاتح", "Listening...": "جارٍ الاستماع...", "Llama.cpp": "Llama.cpp", "LLMs can make mistakes. Verify important information.": "يمكن أن تصدر بعض الأخطاء. لذلك يجب التحقق من المعلومات المهمة", "Loader": "المحمّل", "Loading Kokoro.js...": "جارٍ تحميل Kokoro.js...", "Local": "م<PERSON><PERSON>ي", "Local Task Model": "", "Location access not allowed": "لا يُسمح بالوصول إلى الموقع", "Lost": "ضائع", "LTR": "من جهة اليسار إلى اليمين", "Made by Open WebUI Community": "OpenWebUI تم إنشاؤه بواسطة مجتمع ", "Make password visible in the user interface": "", "Make sure to enclose them with": "تأكد من إرفاقها", "Make sure to export a workflow.json file as API format from ComfyUI.": "تأكد من تصدير ملف workflow.json بصيغة API من ComfyUI.", "Manage": "إدارة", "Manage Direct Connections": "إدارة الاتصالات المباشرة", "Manage Models": "إدارة النماذج", "Manage Ollama": "إدارة Ollama", "Manage Ollama API Connections": "إدارة اتصالات Ollama API", "Manage OpenAI API Connections": "إدارة اتصالات OpenAI API", "Manage Pipelines": "إدارة خطوط الأنابيب", "Manage Tool Servers": "", "March": "مارس", "Markdown": "", "Markdown (Header)": "", "Max Speakers": "", "Max Upload Count": "الح<PERSON> الأق<PERSON>ى لعدد التحميلات", "Max Upload Size": "الح<PERSON> الأقصى لحجم الملف المرفوع", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "يمكن تنزيل 3 نماذج كحد أقصى في وقت واحد. الرجاء معاودة المحاولة في وقت لاحق.", "May": "مايو", "Memories accessible by LLMs will be shown here.": "سيتم عرض الذكريات التي يمكن الوصول إليها بواسطة LLMs هنا.", "Memory": "الذاكرة", "Memory added successfully": "تم إضافة الذاكرة بنجاح", "Memory cleared successfully": "تم مسح الذاكرة بنجاح", "Memory deleted successfully": "تم حذف الذاكرة بنجاح", "Memory updated successfully": "تم تحديث الذاكرة بنجاح", "Merge Responses": "<PERSON><PERSON><PERSON> الردود", "Merged Response": "نتيجة الردود المدمجة", "Message rating should be enabled to use this feature": "يجب تفعيل تقييم الرسائل لاستخدام هذه الميزة", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "لن تتم مشاركة الرسائل التي ترسلها بعد إنشاء الرابط الخاص بك. سيتمكن المستخدمون الذين لديهم عنوان URL من عرض الدردشة المشتركة", "Microsoft OneDrive": "", "Microsoft OneDrive (personal)": "", "Microsoft OneDrive (work/school)": "", "Mistral OCR": "", "Mistral OCR API Key required.": "", "Model": "النموذج", "Model '{{modelName}}' has been successfully downloaded.": "تم تحميل النموذج '{{modelName}}' بنجاح", "Model '{{modelTag}}' is already in queue for downloading.": "النموذج '{{modelTag}}' موجو<PERSON> بالفعل في قائمة الانتظار للتحميل", "Model {{modelId}} not found": "لم يتم العثور على النموذج {{modelId}}.", "Model {{modelName}} is not vision capable": "نموذج {{modelName}} غير قادر على الرؤية", "Model {{name}} is now {{status}}": "نموذج {{name}} هو الآن {{status}}", "Model {{name}} is now hidden": "", "Model {{name}} is now visible": "", "Model accepts file inputs": "", "Model accepts image inputs": "النموذج يقبل إدخالات الصور", "Model can execute code and perform calculations": "", "Model can generate images based on text prompts": "", "Model can search the web for information": "", "Model created successfully!": "تم إنشاء النموذج بنجاح!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "تم اكتشاف مسار نظام الملفات النموذجي. الاسم المختصر للنموذج مطلوب للتحديث، ولا يمكن الاستمرار.", "Model Filtering": "تصفية النماذج", "Model ID": "رقم الموديل", "Model IDs": "معرّفات النماذج", "Model Name": "اسم النموذج", "Model not selected": "لم تختار موديل", "Model Params": "معلمات النموذج", "Model Permissions": "أذونات النموذج", "Model unloaded successfully": "", "Model updated successfully": "تم تحديث النموذج بنجاح", "Model(s) do not support file upload": "", "Modelfile Content": "محتوى الملف النموذجي", "Models": "الموديلات", "Models Access": "الوصول إلى النماذج", "Models configuration saved successfully": "تم حفظ إعدادات النماذج بنجاح", "Models Public Sharing": "", "Mojeek Search API Key": "مفتاح API لـ Mojeek Search", "more": "المزيد", "More": "المزيد", "Name": "الأسم", "Name your knowledge base": "قم بتسمية قاعدة معرفتك", "Native": "أصلي", "New Chat": "دردشة جديدة", "New Folder": "م<PERSON><PERSON><PERSON> جديد", "New Function": "", "New Note": "", "New Password": "كلمة المرور الجديدة", "New Tool": "", "new-channel": "قناة جديدة", "Next message": "", "No chats found for this user.": "", "No chats found.": "", "No content": "", "No content found": "لم يتم العثور على محتوى", "No content found in file.": "", "No content to speak": "لا يوجد محتوى للتحدث عنه", "No distance available": "لا توجد مسافة متاحة", "No feedbacks found": "لم يتم العثور على ملاحظات", "No file selected": "لم يتم تحديد ملف", "No groups with access, add a group to grant access": "لا توجد مجموعات لها حق الوصول، أضف مجموعة لمنح الوصول", "No HTML, CSS, or JavaScript content found.": "لم يتم العثور على محتوى HTML أو CSS أو JavaScript.", "No inference engine with management support found": "لم يتم العثور على محرك استدلال يدعم الإدارة", "No knowledge found": "لم يتم العثور على معرفة", "No memories to clear": "لا توجد ذاكرة لمسحها", "No model IDs": "لا توجد معرّفات نماذج", "No models found": "لم يتم العثور على نماذج", "No models selected": "لم يتم اختيار نماذج", "No Notes": "", "No results found": "لا توجد نتايج", "No search query generated": "لم يتم إنشاء استعلام بحث", "No source available": "لا يوجد مصدر متاح", "No users were found.": "لم يتم العثور على مستخدمين.", "No valves to update": "لا توجد صمامات للتحديث", "None": "اي", "Not factually correct": "ليس صحيحا من حيث الواقع", "Not helpful": "<PERSON>ير مفيد", "Note deleted successfully": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "ملاحظة: إذ<PERSON> قمت بتعيين الحد الأدنى من النقاط، فلن يؤدي البحث إلا إلى إرجاع المستندات التي لها نقاط أكبر من أو تساوي الحد الأدنى من النقاط.", "Notes": "ملاحظات", "Notification Sound": "صوت الإشعارات", "Notification Webhook": "رابط Webhook للإشعارات", "Notifications": "إشعارات", "November": "نوفمبر", "OAuth ID": "معرّف OAuth", "October": "اكتوبر", "Off": "أغلاق", "Okay, Let's Go!": "حسنا دعنا نذهب!", "OLED Dark": "OLED داكن", "Ollama": "Ollama", "Ollama API": "أولاما API", "Ollama API settings updated": "تم تحديث إعدادات واجهة Ollama API", "Ollama Version": "<PERSON><PERSON><PERSON> الاصدار", "On": "تشغيل", "OneDrive": "OneDrive", "Only active when \"Paste Large Text as File\" setting is toggled on.": "", "Only active when the chat input is in focus and an LLM is generating a response.": "", "Only alphanumeric characters and hyphens are allowed": "يُسمح فقط بالحروف والأرقام والواصلات", "Only alphanumeric characters and hyphens are allowed in the command string.": "يُسمح فقط بالأحرف الأبجدية الرقمية والواصلات في سلسلة الأمر.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "يمكن تعديل المجموعات فقط، أنشئ قاعدة معرفة جديدة لتعديل أو إضافة مستندات.", "Only markdown files are allowed": "", "Only select users and groups with permission can access": "يمكن الوصول فقط من قبل المستخدمين والمجموعات المصرح لهم", "Oops! Looks like the URL is invalid. Please double-check and try again.": "خطاء! يبدو أن عنوان URL غير صالح. يرجى التحقق مرة أخرى والمحاولة مرة أخرى.", "Oops! There are files still uploading. Please wait for the upload to complete.": "عذرًا! لا تزال بعض الملفات قيد الرفع. يرجى الانتظار حتى يكتمل الرفع.", "Oops! There was an error in the previous response.": "عذرًا! حدث خطأ في الرد السابق.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "خطاء! أنت تستخدم طريقة غير مدعومة (الواجهة الأمامية فقط). يرجى تقديم واجهة WebUI من الواجهة الخلفية.", "Open file": "فتح الملف", "Open in full screen": "فتح في وضع ملء الشاشة", "Open modal to configure connection": "", "Open new chat": "فتح محادثة جديده", "Open WebUI can use tools provided by any OpenAPI server.": "", "Open WebUI uses faster-whisper internally.": "تستخدم واجهة WebUI أداة faster-whisper داخليًا.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "تستخدم WebUI نموذج SpeechT5 وتضمينات صوتية من CMU Arctic.", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "إصدار WebUI الحالي (v{{OPEN_WEBUI_VERSION}}) أقل من الإصدار المطلوب (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "OpenAI API", "OpenAI API Config": "OpenAI API إعدادات", "OpenAI API Key is required.": "OpenAI API.مطلوب مفتاح ", "OpenAI API settings updated": "تم تحديث إعدادات OpenAI API", "OpenAI URL/Key required.": "URL/مفتاح OpenAI.مطلوب عنوان ", "openapi.json URL or Path": "", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "", "or": "أو", "Ordered List": "", "Organize your users": "تنظيم المستخدمين الخاصين بك", "Other": "آخر", "OUTPUT": "الإخراج", "Output format": "تنسيق الإخراج", "Output Format": "", "Overview": "نظرة عامة", "page": "صفحة", "Paginate": "", "Parameters": "", "Password": "الباسورد", "Paste Large Text as File": "الصق نصًا كبيرًا كملف", "PDF document (.pdf)": "PDF ملف (.pdf)", "PDF Extract Images (OCR)": "PDF أستخرج الصور (OCR)", "pending": "قيد الانتظار", "Pending": "", "Pending User Overlay Content": "", "Pending User Overlay Title": "", "Permission denied when accessing media devices": "تم رفض الإذن عند محاولة الوصول إلى أجهزة الوسائط", "Permission denied when accessing microphone": "تم رفض الإذن عند محاولة الوصول إلى الميكروفون", "Permission denied when accessing microphone: {{error}}": "{{error}} تم رفض الإذن عند الوصول إلى الميكروفون ", "Permissions": "الأذونات", "Perplexity API Key": "مفتاح API لـ Perplexity", "Perplexity Model": "", "Perplexity Search Context Usage": "", "Personalization": "التخصيص", "Picture Description API Config": "", "Picture Description Local Config": "", "Picture Description Mode": "", "Pin": "تثبيت", "Pinned": "مثبت", "Pioneer insights": "رؤى رائدة", "Pipe": "", "Pipeline deleted successfully": "تم حذ<PERSON> خط المعالجة بنجاح", "Pipeline downloaded successfully": "تم تنزيل خط المعالجة بنجاح", "Pipelines": "خطوط الانابيب", "Pipelines Not Detected": "لم يتم الكشف عن خطوط المعالجة", "Pipelines Valves": "صمامات خطوط الأنابيب", "Plain text (.md)": "", "Plain text (.txt)": "نص عادي (.txt)", "Playground": "مكان التجربة", "Playwright Timeout (ms)": "", "Playwright WebSocket URL": "", "Please carefully review the following warnings:": "يرجى مراجعة التحذيرات التالية بعناية:", "Please do not close the settings page while loading the model.": "الرجاء عدم إغلاق صفحة الإعدادات أثناء تحميل النموذج.", "Please enter a prompt": "الرجاء إدخال توجيه", "Please enter a valid path": "", "Please enter a valid URL": "", "Please fill in all fields.": "الرجاء تعبئة جميع الحقول.", "Please select a model first.": "الرجاء اختيار نموذج أولاً.", "Please select a model.": "الرجاء اختيار نموذج.", "Please select a reason": "الرجاء اختيار سبب", "Port": "المنفذ", "Positive attitude": "موق<PERSON> ايجابي", "Prefix ID": "معرف البادئة", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "يُستخدم معرف البادئة لتفادي التعارض مع الاتصالات الأخرى من خلال إضافة بادئة إلى معرفات النماذج – اتركه فارغًا لتعطيله", "Prevent file creation": "", "Preview": "", "Previous 30 days": "أخر 30 يوم", "Previous 7 days": "أخر 7 أيام", "Previous message": "", "Private": "", "Profile Image": "صورة الملف الشخصي", "Prompt": "التوجيه", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "موجه (على سبيل المثال: أخبرني بحقيقة ممتعة عن الإمبراطورية الرومانية)", "Prompt Autocompletion": "", "Prompt Content": "مح<PERSON><PERSON><PERSON> عاجل", "Prompt created successfully": "تم إنشاء التوجيه بنجاح", "Prompt suggestions": "اقتراحات سريعة", "Prompt updated successfully": "تم تحديث التوجيه بنجاح", "Prompts": "مطالبات", "Prompts Access": "الوصول إلى التوجيهات", "Prompts Public Sharing": "", "Public": "", "Pull \"{{searchValue}}\" from Ollama.com": "Ollama.com \"{{searchValue}}\" أسحب من ", "Pull a model from Ollama.com": "Ollama.com سحب الموديل من ", "Query Generation Prompt": "توجيه إنشاء الاستعلام", "RAG Template": "RAG تنمبلت", "Rating": "التقييم", "Re-rank models by topic similarity": "إعادة ترتيب النماذج حسب تشابه الموضوع", "Read": "قراءة", "Read Aloud": "أقراء لي", "Reason": "", "Reasoning Effort": "<PERSON><PERSON><PERSON> الاستدلال", "Record": "", "Record voice": "سجل صوت", "Redirecting you to Open WebUI Community": "OpenWebUI إعادة توجيهك إلى مجتمع ", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "يقلل من احتمال توليد إجابات غير منطقية. القيم الأعلى (مثل 100) تعطي إجابات أكثر تنوعًا، بينما القيم الأدنى (مثل 10) تكون أكثر تحفظًا.", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "أشر إلى نفسك باسم \"المستخدم\" (مثل: \"المستخدم يتعلم الإسبانية\")", "References from": "مراجع من", "Refused when it shouldn't have": "رفض عندما لا ينبغي أن يكون", "Regenerate": "تجديد", "Reindex": "", "Reindex Knowledge Base Vectors": "", "Release Notes": "ملاحظات الإصدار", "Releases": "", "Relevance": "الصلة", "Relevance Threshold": "", "Remember Dismissal": "", "Remove": "إزالة", "Remove {{MODELID}} from list.": "", "Remove file": "", "Remove File": "", "Remove image": "", "Remove Model": "<PERSON><PERSON><PERSON> الموديل", "Remove this tag from list": "", "Rename": "إعادة تسمية", "Reorder Models": "إعادة ترتيب النماذج", "Reply in Thread": "الرد داخل سلسلة الرسائل", "Reranking Engine": "", "Reranking Model": "إعادة تقييم النموذج", "Reset": "إعادة تعيين", "Reset All Models": "إعادة تعيين جميع النماذج", "Reset Upload Directory": "إعادة تعيين مجلد التحميل", "Reset Vector Storage/Knowledge": "إعادة تعيين تخزين المتجهات/المعرفة", "Reset view": "إعادة تعيين العرض", "Response": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "لا يمكن تفعيل إشعارات الردود لأن صلاحيات الموقع مرفوضة. يرجى التوجه إلى إعدادات المتصفح لمنح الصلاحية اللازمة.", "Response splitting": "تقسيم الرد", "Response Watermark": "", "Result": "النتيجة", "Retrieval": "الاسترجاع", "Retrieval Query Generation": "توليد استعلام الاسترجاع", "Rich Text Input for Chat": "إدخال نص منسق للمحادثة", "RK": "RK", "Role": "من<PERSON><PERSON>", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "من اليمين إلى اليسار", "Run": "تنفيذ", "Running": "جارٍ التنفيذ", "Save": "<PERSON><PERSON><PERSON>", "Save & Create": "حفظ وإنشاء", "Save & Update": "حفظ وتحديث", "Save As Copy": "حفظ كنسخة", "Save Tag": "<PERSON><PERSON><PERSON> الوسم", "Saved": "تم الحفظ", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "لم يعد حفظ سجلات الدردشة مباشرة في مساحة تخزين متصفحك مدعومًا. يرجى تخصيص بعض الوقت لتنزيل وحذف سجلات الدردشة الخاصة بك عن طريق النقر على الزر أدناه. لا تقلق، يمكنك بسهولة إعادة استيراد سجلات الدردشة الخاصة بك إلى الواجهة الخلفية من خلاله", "Scroll On Branch Change": "", "Search": "البحث", "Search a model": "البحث عن موديل", "Search Base": "قاعدة البحث", "Search Chats": "البحث في الدردشات", "Search Collection": "البحث في المجموعة", "Search Filters": "مرشحات البحث", "search for tags": "البحث عن وسوم", "Search Functions": "البحث في الوظائف", "Search Knowledge": "البحث في المعرفة", "Search Models": "نماذج البحث", "Search Notes": "", "Search options": "خيارات البحث", "Search Prompts": "<PERSON><PERSON><PERSON><PERSON> حث", "Search Result Count": "عد<PERSON> نتائج البحث", "Search the internet": "البحث في الإنترنت", "Search Tools": "أدوات البحث", "SearchApi API Key": "مفتاح API لـ SearchApi", "SearchApi Engine": "محرك SearchApi", "Searched {{count}} sites": "تم البحث في {{count}} مواقع", "Searching \"{{searchQuery}}\"": "جارٍ البحث عن \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "جارٍ البحث في المعرفة عن \"{{searchQuery}}\"", "Searching the web...": "", "Searxng Query URL": "عنوان URL لاستعلام Searxng", "See readme.md for instructions": "readme.md للحصول على التعليمات", "See what's new": "ما الجديد", "Seed": "Seed", "Select a base model": "حدد نموذجا أساسيا", "Select a engine": "اختر محركًا", "Select a function": "اختر وظيفة", "Select a group": "اختر مجموعة", "Select a model": "أخ<PERSON><PERSON><PERSON> الموديل", "Select a pipeline": "<PERSON><PERSON><PERSON> مسارا", "Select a pipeline url": "حدد عنوان URL لخط الأنابيب", "Select a tool": "اختر أداة", "Select an auth method": "اختر طريقة التوثيق", "Select an Ollama instance": "اختر نسخة Ollama", "Select Engine": "ا<PERSON><PERSON><PERSON> المحرك", "Select Knowledge": "اختر المعرفة", "Select only one model to call": "اختر نموذجًا واحدًا فقط للاستدعاء", "Selected model(s) do not support image inputs": "النموذج (النماذج) المحددة لا تدعم مدخلات الصور", "Semantic distance to query": "المسافة الدلالية إلى الاستعلام", "Send": "تم", "Send a Message": "يُرجى إدخال طلبك هنا", "Send message": "يُرجى إدخال طلبك هنا.", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "يرسل `stream_options: { include_usage: true }` في الطلب.\nالمزودون المدعومون سيُرجعون معلومات استخدام الرموز في الاستجابة عند التفعيل.", "September": "سبتمبر", "SerpApi API Key": "مفتاح API لـ SerpApi", "SerpApi Engine": "محر<PERSON>p<PERSON>pi", "Serper API Key": "مفتاح واجهة برمجة تطبيقات سيربر", "Serply API Key": "مفتاح API لـ Serply", "Serpstack API Key": "مفتاح واجهة برمجة تطبيقات Serpstack", "Server connection verified": "تم التحقق من اتصال الخادم", "Set as default": "الافتراضي", "Set CFG Scale": "ضبط مقياس CFG", "Set Default Model": "تفعيد الموديل الافتراضي", "Set embedding model": "تعيين نموذج التضمين", "Set embedding model (e.g. {{model}})": "ضبط نموذج المتجهات (على سبيل المثال: {{model}})", "Set Image Size": "حجم الصورة", "Set reranking model (e.g. {{model}})": "ضبط نموذج إعادة الترتيب (على سبيل المثال: {{model}})", "Set Sampler": "تعيين العينة", "Set Scheduler": "تعيين المجدول", "Set Steps": "<PERSON>بط الخطوات", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "تعيين عدد الطبقات التي سيتم تفريغها إلى وحدة معالجة الرسومات (GPU). زيادتها قد تحسن الأداء بشكل كبير، لكنها تستهلك طاقة وموارد GPU أكثر.", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "تحديد عدد سلاسل المعالجة المستخدمة في الحساب. هذا الخيار يتحكم في عدد السلاسل لمعالجة الطلبات بالتوازي. زيادته يحسن الأداء تحت الضغط العالي لكنه يستهلك موارد المعالج.", "Set Voice": "<PERSON>بط الصوت", "Set whisper model": "تعيين نموذج Whisper", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "تعيين انحياز ثابت ضد الرموز التي ظهرت مرة واحدة على الأقل. القيم الأعلى (مثل 1.5) تعاقب التكرار بقوة، والأقل (مثل 0.9) تكون أكثر تساهلًا. عند 0 يتم تعطيله.", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "يحدد انحيازًا متدرجًا ضد الرموز لمعاقبة التكرار حسب عدد مرات الظهور. القيم الأعلى (1.5) تعاقب أكثر، والأقل (0.9) تكون أكثر تساهلًا. عند 0 يتم تعطيله.", "Sets how far back for the model to look back to prevent repetition.": "يحدد مدى رجوع النموذج إلى الوراء لتجنب التكرار.", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "يحدد البذرة العشوائية لاستخدامها في التوليد. تعيين قيمة معينة يجعل النموذج ينتج نفس النص لنفس التوجيه.", "Sets the size of the context window used to generate the next token.": "يحدد حجم نافذة السياق المستخدمة لتوليد الرمز التالي.", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "يحدد تسلسلات الإيقاف. عند مواجهتها، سيتوقف النموذج عن التوليد ويُرجع النتيجة. يمكن تحديد أنماط توقف متعددة داخل ملف النموذج.", "Settings": "الاعدادات", "Settings saved successfully!": "تم حفظ الاعدادات بنجاح", "Share": "كشاركة", "Share Chat": "مشاركة الدردشة", "Share to Open WebUI Community": "OpenWebUI شارك في مجتمع", "Sharing Permissions": "", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "", "Show": "<PERSON><PERSON><PERSON>", "Show \"What's New\" modal on login": "عرض نافذة \"ما الجديد\" عند تسجيل الدخول", "Show Admin Details in Account Pending Overlay": "عرض تفاصيل المشرف في نافذة \"الحساب قيد الانتظار\"", "Show All": "", "Show image preview": "", "Show Less": "", "Show Model": "", "Show shortcuts": "إظهار الاختصارات", "Show your support!": "أظهر دعمك!", "Showcased creativity": "أظهر الإبداع", "Sign in": "تسجيل الدخول", "Sign in to {{WEBUI_NAME}}": "سجّل الدخول إلى {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "سجّل الدخول إلى {{WEBUI_NAME}} باستخدام LDAP", "Sign Out": "تسجيل الخروج", "Sign up": "تسجيل", "Sign up to {{WEBUI_NAME}}": "سجّل في {{WEBUI_NAME}}", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to True.": "", "Signing in to {{WEBUI_NAME}}": "جارٍ تسجيل الدخول إلى {{WEBUI_NAME}}", "sk-1234": "sk-1234", "Skip Cache": "", "Skip the cache and re-run the inference. Defaults to False.": "", "Sougou Search API sID": "", "Sougou Search API SK": "", "Source": "المصدر", "Speech Playback Speed": "سرعة تشغيل الصوت", "Speech recognition error: {{error}}": "{{error}} خطأ في التعرف على الكلام", "Speech-to-Text": "", "Speech-to-Text Engine": "محرك تحويل الكلام إلى نص", "Stop": "إيقا<PERSON>", "Stop Generating": "", "Stop Sequence": "وقف التسلسل", "Stream Chat Response": "بث استجابة الدردشة", "Strikethrough": "", "Strip Existing OCR": "", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "", "STT Model": "نموذج تحويل الصوت إلى نص (STT)", "STT Settings": "STT اعدادات", "Stylized PDF Export": "", "Subtitle (e.g. about the Roman Empire)": "(e.g. about the Roman Empire) الترجمة", "Success": "نجاح", "Successfully updated.": "تم التحديث بنجاح", "Suggested": "مقترحات", "Support": "الدعم", "Support this plugin:": "دعم هذا المكون الإضافي:", "Supported MIME Types": "", "Sync directory": "مزامنة المجلد", "System": "النظام", "System Instructions": "تعليمات النظام", "System Prompt": "محادثة النظام", "Tags": "", "Tags Generation": "إنشاء الوسوم", "Tags Generation Prompt": "توجيه إنشاء الوسوم", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "يتم استخدام أخذ العينات بدون ذيل لتقليل تأثير الرموز الأقل احتمالًا. القيمة الأعلى (مثل 2.0) تقلل التأثير أكثر، والقيمة 1.0 تعطل هذا الإعداد.", "Talk to model": "تحدث إلى النموذج", "Tap to interrupt": "اضغط للمقاطعة", "Task List": "", "Task Model": "", "Tasks": "المهام", "Tavily API Key": "مفتاح API لـ Tavily", "Tavily Extract Depth": "", "Tell us more:": "أخبرنا المزيد:", "Temperature": "درجة حرارة", "Temporary Chat": "محادثة مؤقتة", "Text Splitter": "تقسيم النص", "Text-to-Speech": "", "Text-to-Speech Engine": "محرك تحويل النص إلى كلام", "Thanks for your feedback!": "شكرا لملاحظاتك!", "The Application Account DN you bind with for search": "DN لحساب التطبيق الذي تستخدمه للبحث", "The base to search for users": "الأساس الذي يُستخدم للبحث عن المستخدمين", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "يحدد حجم الدفعة عدد طلبات النصوص التي تتم معالجتها معًا. الحجم الأكبر يمكن أن يزيد الأداء والسرعة، ولكنه يحتاج أيضًا إلى ذاكرة أكبر.", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "المطورون خلف هذا المكون الإضافي هم متطوعون شغوفون من المجتمع. إذا وجدت هذا المكون مفيدًا، فكر في المساهمة في تطويره.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "قائمة التقييم تعتمد على نظام Elo ويتم تحديثها في الوقت الفعلي.", "The format to return a response in. Format can be json or a JSON schema.": "", "The height in pixels to compress images to. Leave empty for no compression.": "", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "", "The LDAP attribute that maps to the mail that users use to sign in.": "السمة LDAP التي تتوافق مع البريد الإلكتروني الذي يستخدمه المستخدمون لتسجيل الدخول.", "The LDAP attribute that maps to the username that users use to sign in.": "السمة LDAP التي تتوافق مع اسم المستخدم الذي يستخدمه المستخدمون لتسجيل الدخول.", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "لوحة المتصدرين حالياً في وضع تجريبي، وقد نقوم بتعديل حسابات التصنيف أثناء تحسين الخوارزمية.", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "الحد الأقصى لحجم الملف بالميغابايت. إذا تجاوز الملف هذا الحد، فلن يتم رفعه.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "الحد الأقصى لعدد الملفات التي يمكن استخدامها في المحادثة دفعة واحدة. إذا تجاوز العدد هذا الحد، فلن يتم رفع الملفات.", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "يجب أن تكون النتيجة قيمة تتراوح بين 0.0 (0%) و1.0 (100%).", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "درجة حرارة النموذج. زيادتها تجعل الإجابات أكثر إبداعًا.", "The width in pixels to compress images to. Leave empty for no compression.": "", "Theme": "الثيم", "Thinking...": "جارٍ التفكير...", "This action cannot be undone. Do you wish to continue?": "لا يمكن التراجع عن هذا الإجراء. هل ترغب في المتابعة؟", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "", "This chat won't appear in history and your messages will not be saved.": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "وهذا يضمن حفظ محادثاتك القيمة بشكل آمن في قاعدة بياناتك الخلفية. شكرًا لك!", "This feature is experimental and may be modified or discontinued without notice.": "", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "هذه ميزة تجريبية، وقد لا تعمل كما هو متوقع وقد تتغير في أي وقت.", "This model is not publicly available. Please select another model.": "", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "هذا الخيار يحدد عدد الرموز التي يتم الاحتفاظ بها عند تحديث السياق. مثلاً، إذا تم ضبطه على 2، سيتم الاحتفاظ بآخر رمزين من السياق. الحفاظ على السياق يساعد في استمرارية المحادثة، لكنه قد يحد من التفاعل مع مواضيع جديدة.", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "يحدد هذا الخيار الحد الأقصى لعدد الرموز التي يمكن للنموذج توليدها في الرد. زيادته تتيح للنموذج تقديم إجابات أطول، لكنها قد تزيد من احتمالية توليد محتوى غير مفيد أو غير ذي صلة.", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "سيؤدي هذا الخيار إلى حذف جميع الملفات الحالية في المجموعة واستبدالها بالملفات التي تم تحميلها حديثًا.", "This response was generated by \"{{model}}\"": "تم توليد هذا الرد بواسطة \"{{model}}\"", "This will delete": "هذا سيقوم بالحذف", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "هذا سيحذف <strong>{{NAME}}</strong> و<strong>كل محتوياته</strong>.", "This will delete all models including custom models": "هذا سيحذف جميع النماذج بما في ذلك النماذج المخصصة", "This will delete all models including custom models and cannot be undone.": "هذا سيحذف جميع النماذج بما في ذلك المخصصة ولا يمكن التراجع عن هذا الإجراء.", "This will reset the knowledge base and sync all files. Do you wish to continue?": "هذا سيؤدي إلى إعادة تعيين قاعدة المعرفة ومزامنة جميع الملفات. هل ترغب في المتابعة؟", "Thorough explanation": "شرح شامل", "Thought for {{DURATION}}": "فكّر لمدة {{DURATION}}", "Thought for {{DURATION}} seconds": "فكّر لمدة {{DURATION}} ثانية", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "عنوان خادم Tika مطلوب.", "Tiktoken": "Tiktoken", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "ملاحضة: قم بتحديث عدة فتحات متغيرة على التوالي عن طريق الضغط على مفتاح tab في مدخلات الدردشة بعد كل استبدال.", "Title": "العنوان", "Title (e.g. Tell me a fun fact)": "(e.g. Tell me a fun fact) العناون", "Title Auto-Generation": "توليد تلقائي للعنوان", "Title cannot be an empty string.": "العنوان مطلوب", "Title Generation": "توليد العنوان", "Title Generation Prompt": "موجه إنشاء العنوان", "TLS": "TLS", "To access the available model names for downloading,": "للوصول إلى أسماء الموديلات المتاحة للتنزيل،", "To access the GGUF models available for downloading,": "للوصول إلى الموديلات GGUF المتاحة للتنزيل،", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "للوصول إلى واجهة WebUI، يُرجى التواصل مع المسؤول. يمكن للمسؤولين إدارة حالة المستخدمين من لوحة الإدارة.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "لإرفاق قاعدة المعرفة هنا، أضفها أولاً إلى مساحة العمل \"المعرفة\".", "To learn more about available endpoints, visit our documentation.": "لمعرفة المزيد حول نقاط النهاية المتاحة، قم بزيارة الوثائق الخاصة بنا.", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "لحماية خصوصيتك، يتم مشاركة التقييمات ومعرّفات النماذج والوسوم والبيانات الوصفية فقط من ملاحظاتك—سجلات الدردشة تظل خاصة ولا تُدرج.", "To select actions here, add them to the \"Functions\" workspace first.": "لاختيار الإجراءات هنا، أضفها أولاً إلى مساحة العمل \"الوظائف\".", "To select filters here, add them to the \"Functions\" workspace first.": "لاختيار الفلاتر هنا، أضفها أولاً إلى مساحة العمل \"الوظائف\".", "To select toolkits here, add them to the \"Tools\" workspace first.": "لاختيار الأدوات هنا، أضفها أولاً إلى مساحة العمل \"الأدوات\".", "Toast notifications for new updates": "إشعارات منبثقة للتحديثات الجديدة", "Today": "اليوم", "Toggle search": "", "Toggle settings": "فتح وأغلاق الاعدادات", "Toggle sidebar": "فتح وأغلاق الشريط الجانبي", "Toggle whether current connection is active.": "", "Token": "<PERSON><PERSON><PERSON>", "Too verbose": "مفرط في التفاصيل", "Tool created successfully": "تم إنشاء الأداة بنجاح", "Tool deleted successfully": "تم حذف الأداة بنجاح", "Tool Description": "وصف الأداة", "Tool ID": "معرف الأداة", "Tool imported successfully": "تم استيراد الأداة بنجاح", "Tool Name": "اسم الأداة", "Tool Servers": "", "Tool updated successfully": "تم تحديث الأداة بنجاح", "Tools": "الأدوات", "Tools Access": "الوصول إلى الأدوات", "Tools are a function calling system with arbitrary code execution": "الأدوات عبارة عن نظام لاستدعاء الوظائف يسمح بتنفيذ كود برمجي مخصص", "Tools Function Calling Prompt": "توجيه استدعاء وظائف الأدوات", "Tools have a function calling system that allows arbitrary code execution.": "تحتوي الأدوات على نظام لاستدعاء الوظائف يتيح تنفيذ كود برمجي مخصص.", "Tools Public Sharing": "", "Top K": "Top K", "Top K Reranker": "", "Transformers": "Transformers", "Trouble accessing Ollama?": "هل تواجه مشكلة في الوصول", "Trust Proxy Environment": "بيئة البروكسي الموثوقة", "TTS Model": "نموذج تحويل النص إلى كلام (TTS)", "TTS Settings": "TTS اعدادات", "TTS Voice": "صوت TTS", "Type": "نوع", "Type Hugging Face Resolve (Download) URL": "اكتب عنوان URL لحل مشكلة الوجه (تنزيل).", "Uh-oh! There was an issue with the response.": "أوه! حدثت مشكلة في الرد.", "UI": "واجهة المستخدم", "Unarchive All": "إلغاء أرشفة الكل", "Unarchive All Archived Chats": "إلغاء أرشفة جميع المحادثات المؤرشفة", "Unarchive Chat": "إلغاء أرشفة المحادثة", "Underline": "", "Unloads {{FROM_NOW}}": "", "Unlock mysteries": "اكشف الأسرار", "Unpin": "إزالة التثبيت", "Unravel secrets": "فكّ الأسرار", "Untagged": "بدون وسوم", "Untitled": "", "Update": "تحديث", "Update and Copy Link": "تحديث ونسخ الرابط", "Update for the latest features and improvements.": "حدّث للحصول على أحدث الميزات والتحسينات.", "Update password": "تحديث كلمة المرور", "Updated": "تم التحديث", "Updated at": "تم التحديث في", "Updated At": "تم التحديث في", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "قم بالترقية إلى خطة مرخصة للحصول على ميزات إضافية، مثل التخصيص والدعم المخصص.", "Upload": "رفع", "Upload a GGUF model": "GGUF رفع موديل نوع", "Upload Audio": "", "Upload directory": "ر<PERSON><PERSON> مجلد", "Upload files": "رفع ملفات", "Upload Files": "تحميل الملفات", "Upload Pipeline": "ر<PERSON><PERSON> خط المعالجة", "Upload Progress": "جاري التحميل", "URL": "الرابط", "URL Mode": "رابط الموديل", "Usage": "", "Use '#' in the prompt input to load and include your knowledge.": "استخدم الرمز '#' في خانة التوجيه لتحميل وإدراج المعرفة الخاصة بك.", "Use Gravatar": "<PERSON>ra<PERSON><PERSON> أستخدم", "Use groups to group your users and assign permissions.": "استخدم المجموعات لتجميع المستخدمين وتحديد الصلاحيات.", "Use Initials": "Initials أستخدم", "Use LLM": "", "Use no proxy to fetch page contents.": "", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "", "user": "مستخدم", "User": "مستخدم", "User location successfully retrieved.": "تم استرجاع موقع المستخدم بنجاح.", "User menu": "", "User Webhooks": "", "Username": "اسم المستخدم", "Users": "المستخدمين", "Using the default arena model with all models. Click the plus button to add custom models.": "يتم استخدام نموذج الساحة الافتراضي مع جميع النماذج. اضغط على زر + لإضافة نماذج مخصصة.", "Utilize": "يستخدم", "Valid time units:": "وحدات زمنية صالحة:", "Valves": "الصمامات", "Valves updated": "تم تحديث الصمامات", "Valves updated successfully": "تم تحديث الصمامات بنجاح", "variable": "المتغير", "variable to have them replaced with clipboard content.": "متغير لاستبدالها بمحتوى الحافظة.", "Verify Connection": "", "Verify SSL Certificate": "", "Version": "إصدار", "Version {{selectedVersion}} of {{totalVersions}}": "الإصدار {{selectedVersion}} من {{totalVersions}}", "View Replies": "عر<PERSON> الردود", "View Result from **{{NAME}}**": "", "Visibility": "مستوى الظهور", "Vision": "", "Voice": "الصوت", "Voice Input": "إد<PERSON>ال صوتي", "Voice mode": "", "Warning": "تحذير", "Warning:": "تحذير:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "تحذير: تفعيل هذا الخيار سيسمح للمستخدمين برفع كود عشوائي على الخادم.", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "تحذير: إذ<PERSON> قمت بتحديث أو تغيير نموذج التضمين الخاص بك، فستحتاج إلى إعادة استيراد كافة المستندات.", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "تحذير: تن<PERSON><PERSON><PERSON> كود <PERSON> يتيح تنفيذ كود عشوائي مما يشكل مخاطر أمنية جسيمة—تابع بحذر شديد.", "Web": "Web", "Web API": "واجهة برمجة التطبيقات (API)", "Web Loader Engine": "", "Web Search": "<PERSON><PERSON><PERSON> الويب", "Web Search Engine": "محرك بحث الويب", "Web Search in Chat": "بحث ويب داخل المحادثة", "Web Search Query Generation": "توليد استعلام بحث الويب", "Webhook URL": "Webhook الرابط", "WebUI Settings": "WebUI اعدادات", "WebUI URL": "رابط WebUI", "WebUI will make requests to \"{{url}}\"": "", "WebUI will make requests to \"{{url}}/api/chat\"": "ستقوم WebUI بإرسال الطلبات إلى \"{{url}}/api/chat\"", "WebUI will make requests to \"{{url}}/chat/completions\"": "ستقوم WebUI بإرسال الطلبات إلى \"{{url}}/chat/completions\"", "Weight of BM25 Retrieval": "", "What are you trying to achieve?": "ما الذي تحاول تحقيقه؟", "What are you working on?": "على ماذا تعمل؟", "What's New in": "ما هو الجديد", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "عند التفعيل، سيستجيب النموذج لكل رسالة في المحادثة بشكل فوري، مولدًا الرد بمجرد إرسال المستخدم لرسالته. هذا الوضع مفيد لتطبيقات الدردشة الحية، لكنه قد يؤثر على الأداء في الأجهزة الأبطأ.", "wherever you are": "أينما كنت", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "", "Whisper (Local)": "<PERSON><PERSON><PERSON> (مح<PERSON><PERSON>)", "Why?": "لماذا؟", "Widescreen Mode": "وضع الشاشة العريضة", "Won": "فاز", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "يعمل جنبًا إلى جنب مع top-k. القيمة الأعلى (مثلاً 0.95) تنتج نصًا أكثر تنوعًا، بينما القيمة الأقل (مثلاً 0.5) تنتج نصًا أكثر تركيزًا وتحفظًا.", "Workspace": "مساحة العمل", "Workspace Permissions": "صلاحيات مساحة العمل", "Write": "كتابة", "Write a prompt suggestion (e.g. Who are you?)": "اكتب اقتراحًا سريعًا (على سبيل المثال، من أنت؟)", "Write a summary in 50 words that summarizes [topic or keyword].": "اكتب ملخصًا في 50 كلمة يلخص [الموضوع أو الكلمة الرئيسية]", "Write something...": "اكتب شيئًا...", "Yacy Instance URL": "", "Yacy Password": "", "Yacy Username": "", "Yesterday": "<PERSON><PERSON><PERSON>", "You": "انت", "You are currently using a trial license. Please contact support to upgrade your license.": "أنت تستخدم حالياً ترخيصًا تجريبيًا. يُرجى التواصل مع الدعم للترقية.", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "يمكنك الدردشة مع {{maxCount}} مل<PERSON>(ات) كحد أقصى في نفس الوقت.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "يمكنك تخصيص تفاعلك مع النماذج اللغوية عن طريق إضافة الذكريات باستخدام زر \"إدارة\" أدناه، مما يجعلها أكثر فائدة وتناسبًا لك.", "You cannot upload an empty file.": "لا يمكنك رفع ملف فارغ.", "You do not have permission to upload files.": "ليس لديك صلاحية لرفع الملفات.", "You have no archived conversations.": "لا تملك محادثات محفوظه", "You have shared this chat": "تم مشاركة هذه المحادثة", "You're a helpful assistant.": "مساعدك المفيد هنا", "You're now logged in.": "لق<PERSON> قمت الآن بتسجيل الدخول.", "Your account status is currently pending activation.": "حالة حسابك حالياً بانتظار التفعيل.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "سيتم توجيه كامل مساهمتك مباشرة إلى مطور المكون الإضافي؛ لا تأخذ Open WebUI أي نسبة. ومع ذلك، قد تفرض منصة التمويل المختارة رسومًا خاصة بها.", "Youtube": "Youtube", "Youtube Language": "لغة YouTube", "Youtube Proxy URL": "رابط بروكسي YouTube"}
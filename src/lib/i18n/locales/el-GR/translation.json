{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(π.χ. `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(π.χ. `sh webui.sh --api`)", "(latest)": "(τελευταίο)", "(leave blank for to use commercial endpoint)": "", "[Last] dddd [at] h:mm A": "", "[Today at] h:mm A": "", "[Yesterday at] h:mm A": "", "{{ models }}": "{{ models }}", "{{COUNT}} Available Tools": "", "{{COUNT}} characters": "", "{{COUNT}} hidden lines": "", "{{COUNT}} Replies": "", "{{COUNT}} words": "", "{{user}}'s Chats": "Συνομιλίες του {{user}}", "{{webUIName}} Backend Required": "{{webUIName}} Απαιτείται Backend", "*Prompt node ID(s) are required for image generation": "*Τα αναγνωριστι<PERSON>ά κόμβου Prompt απαιτούνται για τη δημιουργία εικόνων", "A new version (v{{LATEST_VERSION}}) is now available.": "Μια νέα έκδοση (v{{LATEST_VERSION}}) είναι τώρα διαθέσιμη.", "A task model is used when performing tasks such as generating titles for chats and web search queries": "Ένα μοντέλο εργασίας χρησιμοποιείται κατά την εκτέλεση εργασιών όπως η δημιουργία τίτλων για συνομιλίες και αναζητήσεις στο διαδίκτυο", "a user": "ένας χρήστης", "About": "Σχετικά", "Accept autocomplete generation / Jump to prompt variable": "", "Access": "Πρόσβαση", "Access Control": "Έλεγχος Πρόσβασης", "Accessible to all users": "Προσβά<PERSON>ι<PERSON>ο σε όλους τους χρήστες", "Account": "Λογ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ς", "Account Activation Pending": "Ενεργοποίηση Λογαριασμού Εκκρεμεί", "Accurate information": "Ακριβείς πληροφορίες", "Action": "", "Actions": "Ενέργειες", "Activate": "", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "Ενεργοποιήστε αυτή την εντολή πληκτρολογώντας \"/{{COMMAND}}\" στο πεδίο συνομιλίας.", "Active Users": "Ενεργοί Χρήστες", "Add": "Προσθήκη", "Add a model ID": "Προσθήκη αναγνωριστικού μοντέλου", "Add a short description about what this model does": "Προσθήκη σύντομης περιγραφής για το τι κάνει αυτό το μοντέλο", "Add a tag": "Προσθήκη ετικέτας", "Add Arena Model": "Προσθήκη Μοντέλου Arena", "Add Connection": "Προσθήκη Σύνδεσης", "Add Content": "Προσθήκη Περιεχομένου", "Add content here": "Προσθέστε περιεχόμενο εδώ", "Add Custom Parameter": "", "Add custom prompt": "Προσθήκη προσαρμοσμένης προτροπής", "Add Files": "Προσθήκη Αρχείων", "Add Group": "Προσθήκη Ομάδας", "Add Memory": "Προσθήκη Μνήμης", "Add Model": "Προσθήκη Μοντέλου", "Add Reaction": "", "Add Tag": "Προσθήκη Ετικέτας", "Add Tags": "Προσθήκη Ετικετών", "Add text content": "Προσθήκη κειμένου", "Add User": "Προσθήκη Χρήστη", "Add User Group": "Προσθήκη Ομάδα<PERSON>ν", "Adjusting these settings will apply changes universally to all users.": "Η ρύθμιση αυτών των παραμέτρων θα εφαρμόσει τις αλλαγές καθολικά σε όλους τους χρήστες.", "admin": "διαχειριστής", "Admin": "Διαχειριστής", "Admin Panel": "Πίνα<PERSON><PERSON><PERSON> Διαχειριστή", "Admin Settings": "Ρυθμίσεις Διαχειριστή", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "Οι διαχειριστές έχουν πρόσβαση σε όλα τα εργαλεία ανά πάσα στιγμή· οι χρήστες χρειάζονται εργαλεία ανά μοντέλο στον χώρο εργασίας.", "Advanced Parameters": "Προηγμένοι Παράμετροι", "Advanced Params": "Προηγμένα Παράμετροι", "AI": "", "All": "", "All Documents": "Όλα τα Έγγραφα", "All models deleted successfully": "Όλα τα μοντέλα διαγράφηκαν με επιτυχία", "Allow Call": "", "Allow Chat Controls": "", "Allow Chat Delete": "Επιτρέπεται η διαγραφή συνομιλίας", "Allow Chat Deletion": "Επιτρέπεται η Διαγραφή Συνομιλίας", "Allow Chat Edit": "Επιτρέπεται η Επεξεργασία Συνομιλίας", "Allow Chat Export": "", "Allow Chat Share": "", "Allow Chat System Prompt": "", "Allow File Upload": "Επιτρέπετα<PERSON> η Αποστολή Αρχείων", "Allow Multiple Models in Chat": "", "Allow non-local voices": "Επιτρέπονται μη τοπικές φωνές", "Allow Speech to Text": "", "Allow Temporary Chat": "Επιτρέπεται η Προσωρινή Συνομιλία", "Allow Text to Speech": "", "Allow User Location": "Επιτρέπεται η Τοποθεσία Χρήστη", "Allow Voice Interruption in Call": "Επιτρέπεται η Παύση Φωνής στην Κλήση", "Allowed Endpoints": "", "Allowed File Extensions": "", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "", "Already have an account?": "Έχετε ήδη λογαριασμό;", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "", "Always": "", "Always Collapse Code Blocks": "", "Always Expand Details": "", "Always Play Notification Sound": "", "Amazing": "Καταπληκτικό", "an assistant": "<PERSON>νας βοηθός", "Analyzed": "", "Analyzing...": "", "and": "και", "and {{COUNT}} more": "και {{COUNT}} ακόμα", "and create a new shared link.": "και δημιουργήστε έναν νέο κοινόχρηστο σύνδεσμο.", "Android": "", "API": "", "API Base URL": "API Βασικό URL", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "", "API Key": "Κλειδί API", "API Key created.": "Το κλειδί API δημιουργήθηκε.", "API Key Endpoint Restrictions": "", "API keys": "κλειδιά API", "API Version": "", "Application DN": "DN Εφαρμογής", "Application DN Password": "Κωδικός DN Εφαρμογής", "applies to all users with the \"user\" role": "εφαρμόζεται σε όλους τους χρήστες με το ρόλο \"user\"", "April": "Απρίλιος", "Archive": "Αρχείο", "Archive All Chats": "Αρχειοθέτηση Όλων των Συνομιλιών", "Archived Chats": "Αρχειοθετημένες Συνομιλίες", "archived-chat-export": "εξαγωγή-αρχείου-συνομιλίας", "Are you sure you want to clear all memories? This action cannot be undone.": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "Είστε σίγουροι ότι θέλετε να απο-αρχειοθετήσετε όλες τις αρχειοθετημένες συνομιλίες;", "Are you sure?": "Είστε σίγουροι;", "Arena Models": "Μοντέλα Arena", "Artifacts": "Αρχεία", "Ask": "", "Ask a question": "Ρωτήστε μια ερώτηση", "Assistant": "<PERSON><PERSON><PERSON><PERSON><PERSON>ς", "Attach file from knowledge": "", "Attention to detail": "Προσοχή στη λεπτομέρεια", "Attribute for Mail": "", "Attribute for Username": "Ιδιότητα για Όνομα Χρήστη", "Audio": "Ήχος", "August": "Αύγουστος", "Auth": "", "Authenticate": "Επαλήθευση", "Authentication": "", "Auto": "", "Auto-Copy Response to Clipboard": "Αυτόματη Αντιγρα<PERSON>ή Απάντησης στο Πρόχειρο", "Auto-playback response": "Αυτόματη αναπαραγωγή της απάντησης", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "Automatic1111", "AUTOMATIC1111 Api Auth String": "Σειρά Επαλήθευσης API AUTOMATIC1111", "AUTOMATIC1111 Base URL": "Βασικό URL AUTOMATIC1111", "AUTOMATIC1111 Base URL is required.": "Απαιτείται το Βασικό URL AUTOMATIC1111.", "Available list": "Διαθέσιμη λίστα", "Available Tools": "", "available!": "διαθέσιμο!", "Awful": "Ασχημο", "Azure AI Speech": "Ομιλία Azure AI", "Azure Region": "Περιοχή Azure", "Back": "Πίσω", "Bad Response": "Κακή Απάντηση", "Banners": "Προβολές", "Base Model (From)": "Βασικ<PERSON> Μον<PERSON>έλ<PERSON> (Από)", "Base Model List Cache speeds up access by fetching base models only at startup or on settings save—faster, but may not show recent base model changes.": "", "before": "πριν", "Being lazy": "Τρώλακας", "Beta": "", "Bing Search V7 Endpoint": "Τέλος Bing Search V7", "Bing Search V7 Subscription Key": "Κλειδί Συνδρομής Bing Search V7", "Bocha Search API Key": "", "Bold": "", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "", "Brave Search API Key": "Κλειδί API Brave Search", "Bullet List": "", "By {{name}}": "Από {{name}}", "Bypass Embedding and Retrieval": "", "Bypass Web Loader": "", "Cache Base Model List": "", "Calendar": "", "Call": "Κλήση", "Call feature is not supported when using Web STT engine": "Η λειτουργία κλήσης δεν υποστηρίζεται όταν χρησιμοποιείται η μηχανή Web STT", "Camera": "Κάμερα", "Cancel": "Ακύρωση", "Capabilities": "Δυνατότητες", "Capture": "", "Capture Audio": "", "Certificate Path": "Διαδρο<PERSON><PERSON>στοποιητικού", "Change Password": "Αλλαγ<PERSON>ωδικού", "Channel Name": "", "Channels": "", "Character": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Character limit for autocomplete generation input": "", "Chart new frontiers": "Σχεδιάστε νέους ορίζοντες", "Chat": "Συνομιλία", "Chat Background Image": "Εικόνα Φόντου Συνομιλίας", "Chat Bubble UI": "Διεπα<PERSON><PERSON> Φούσκας Συνομιλίας", "Chat Controls": "Έλεγχοι Συνομιλίας", "Chat direction": "Κατεύθυνση Συνομιλίας", "Chat Overview": "Επισκόπηση Συνομιλίας", "Chat Permissions": "Δικαιώματα Συνομιλίας", "Chat Tags Auto-Generation": "Αυτόματη Γενιά Ετικετών Συνομιλίας", "Chats": "Συνομιλίες", "Check Again": "Ελέγξτε ξανά", "Check for updates": "Έλεγχος για ενημερώσεις", "Checking for updates...": "Ελέγχεται για ενημερώσεις...", "Choose a model before saving...": "Επιλέξτε ένα μοντέλο πριν αποθηκεύσετε...", "Chunk Overlap": "Επικάλυψη Τμημάτων", "Chunk Size": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Τμημάτων", "Ciphers": "Κρυπτογραφήσεις", "Citation": "Παράθεση", "Citations": "", "Clear memory": "Καθαρισμός μνήμης", "Clear Memory": "", "click here": "κλικ εδώ", "Click here for filter guides.": "Κάντε κλικ εδώ για οδηγούς φίλτρων.", "Click here for help.": "Κάντε κλικ εδώ για βοήθεια.", "Click here to": "Κάντε κλικ εδώ για να", "Click here to download user import template file.": "Κάντε κλικ εδώ για να κατεβάσετε το αρχείο προτύπου εισαγωγής χρήστη.", "Click here to learn more about faster-whisper and see the available models.": "Κάντε κλικ εδώ για να μάθετε περισσότερα σχετικά με το faster-whisper και να δείτε τα διαθέσιμα μοντέλα.", "Click here to see available models.": "", "Click here to select": "Κάντε κλικ εδώ για επιλογή", "Click here to select a csv file.": "Κάντε κλικ εδώ για να επιλέξετε ένα αρχείο csv.", "Click here to select a py file.": "Κάντε κλικ εδώ για να επιλέξετε ένα αρχείο py.", "Click here to upload a workflow.json file.": "Κάντε κλικ εδώ για να ανεβάσετε ένα αρχείο workflow.json.", "click here.": "κλικ εδώ.", "Click on the user role button to change a user's role.": "Κάντε κλικ στο κουμπί ρόλου χρήστη για να αλλάξετε το ρόλο ενός χρήστη.", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "Άρνηση δικαιώματος εγγραφής στο πρόχειρο. Πα<PERSON>α<PERSON><PERSON><PERSON><PERSON> ελέγξτε τις ρυθμίσεις του περιηγητή σας για να δώσετε την απαραίτητη πρόσβαση.", "Clone": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Clone Chat": "", "Clone of {{TITLE}}": "", "Close": "Κλείσιμο", "Close Configure Connection Modal": "", "Close modal": "", "Close settings modal": "", "Code Block": "", "Code execution": "Εκτέλεση κώδικα", "Code Execution": "", "Code Execution Engine": "", "Code Execution Timeout": "", "Code formatted successfully": "Ο κώδικας μορφοποιήθηκε επιτυχώς", "Code Interpreter": "", "Code Interpreter Engine": "", "Code Interpreter Prompt Template": "", "Collapse": "", "Collection": "Συλλογή", "Color": "Χρώμα", "ComfyUI": "ComfyUI", "ComfyUI API Key": "", "ComfyUI Base URL": "Βασικό URL ComfyUI", "ComfyUI Base URL is required.": "Απαιτείται το Βασικό URL ComfyUI.", "ComfyUI Workflow": "Ροές Εργασίας ComfyUI", "ComfyUI Workflow Nodes": "Κόμβοι Ροής Εργασίας ComfyUI", "Command": "Εντολή", "Comment": "", "Completions": "Ολοκληρώσεις", "Concurrent Requests": "Ταυτόχρονες Αιτήσεις", "Configure": "Διαμόρφωση", "Confirm": "Επιβεβαίωση", "Confirm Password": "Επιβεβαίωση Κωδικού", "Confirm your action": "Επιβεβαιώστε την ενέργειά σας", "Confirm your new password": "", "Connect to your own OpenAI compatible API endpoints.": "", "Connect to your own OpenAPI compatible external tool servers.": "", "Connection failed": "", "Connection successful": "", "Connection Type": "", "Connections": "Συνδέσεις", "Connections saved successfully": "", "Connections settings updated": "", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "", "Contact Admin for WebUI Access": "Επικοινωνήστε με τον Διαχειριστή για Πρόσβαση στο WebUI", "Content": "Περιεχόμενο", "Content Extraction Engine": "", "Continue Response": "Συνέχεια Απάντησης", "Continue with {{provider}}": "Συνέχεια με {{provider}}", "Continue with Email": "Συνέχεια με Email", "Continue with LDAP": "Συνέχεια με LDAP", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "Έλεγχος πώς διαχωρίζεται το κείμενο του μηνύματος για αιτήματα TTS. Το 'Στίξη' διαχωρίζει σε προτάσεις, οι 'παραγράφοι' σε παραγράφους, και το 'κανένα' κρατά το μήνυμα ως μια αλυσίδα.", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "", "Controls": "Έλεγχοι", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "", "Copied": "Αντιγράφηκε", "Copied link to clipboard": "", "Copied shared chat URL to clipboard!": "Αντιγράφηκε το URL της κοινόχρηστης συνομιλίας στο πρόχειρο!", "Copied to clipboard": "Αντιγρά<PERSON>ηκε στο πρόχειρο", "Copy": "Αντιγραφή", "Copy Formatted Text": "", "Copy last code block": "Αντιγραφή τελευταίου μπλοκ κώδικα", "Copy last response": "Αντιγραφή τελευτα<PERSON><PERSON>ς απάντησης", "Copy link": "", "Copy Link": "Αντιγρα<PERSON><PERSON> Συνδέσμου", "Copy to clipboard": "Αντιγρα<PERSON><PERSON> στο πρόχειρο", "Copying to clipboard was successful!": "Η αντιγραφή στο πρόχειρο ήταν επιτυχής!", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "", "Create": "Δημιουργία", "Create a knowledge base": "Δημιουρ<PERSON><PERSON><PERSON> βάσης γνώσης", "Create a model": "Δημιουρ<PERSON><PERSON><PERSON> μοντέλου", "Create Account": "Δημιουργ<PERSON>α Λογαριασμού", "Create Admin Account": "Δημιουργ<PERSON><PERSON> Λογαριασμού Διαχειριστή", "Create Channel": "", "Create Group": "Δημιουργ<PERSON>α Ομάδας", "Create Knowledge": "Δημιουργ<PERSON><PERSON>ώ<PERSON>ης", "Create new key": "Δημιουργ<PERSON>α νέου κλειδιού", "Create new secret key": "Δημιουργ<PERSON>α νέου μυστικού κλειδιού", "Create Note": "", "Create your first note by clicking on the plus button below.": "", "Created at": "Δημιουργήθηκε στις", "Created At": "Δημιουργήθηκε στις", "Created by": "Δημιουργήθηκε από", "CSV Import": "Εισαγωγή CSV", "Ctrl+Enter to Send": "", "Current Model": "Τρέχον Μοντέλο", "Current Password": "Τρέχων <PERSON>δικ<PERSON>ς", "Custom": "Προσαρμοσμένο", "Custom description enabled": "", "Custom Parameter Name": "", "Custom Parameter Value": "", "Danger Zone": "", "Dark": "Σκούρο", "Database": "Βάση Δεδομένων", "Datalab Marker API": "", "Datalab Marker API Key required.": "", "DD/MM/YYYY": "", "December": "Δεκέμβριος", "Default": "Προεπιλογή", "Default (Open AI)": "Προεπιλογή (Open AI)", "Default (SentenceTransformers)": "Προεπιλογή (SentenceTransformers)", "Default description enabled": "", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "", "Default Model": "Προεπιλεγ<PERSON><PERSON><PERSON><PERSON>λο", "Default model updated": "Το προεπιλεγμένο μοντέλο ενημερώθηκε", "Default Models": "Προεπιλεγμένα Μοντέλα", "Default permissions": "Προεπιλεγμένα δικαιώματα", "Default permissions updated successfully": "Τα προεπιλεγμένα δικαιώματα ενημερώθηκαν με επιτυχία", "Default Prompt Suggestions": "Προεπιλεγμένες Προτάσεις Προτροπής", "Default to 389 or 636 if TLS is enabled": "Προεπιλογ<PERSON> στο 389 ή 636 εάν είναι ενεργοποιημένο το TLS", "Default to ALL": "Προεπιλογή σε ΟΛΑ", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "", "Default User Role": "Προεπιλεγ<PERSON>ένος Ρόλος Χρήστη", "Delete": "Διαγραφή", "Delete a model": "Διαγρα<PERSON><PERSON> ενός μοντέλου", "Delete All Chats": "Διαγραφή Όλων των Συνομιλιών", "Delete All Models": "Διαγραφή Όλων των Μοντέλων", "Delete chat": "Διαγραφή συνομιλίας", "Delete Chat": "Διαγρα<PERSON><PERSON> Συνομιλίας", "Delete chat?": "Διαγραφή συνομιλίας;", "Delete folder?": "Διαγρα<PERSON><PERSON> φακέλου;", "Delete function?": "Διαγρα<PERSON><PERSON> λειτουργίας;", "Delete Message": "", "Delete message?": "", "Delete note?": "", "Delete prompt?": "Διαγραφή προτροπής;", "delete this link": "διαγρα<PERSON>ή αυτού του συνδέσμου", "Delete tool?": "Διαγρα<PERSON>ή εργαλείου;", "Delete User": "Διαγρα<PERSON><PERSON>η", "Deleted {{deleteModelTag}}": "Διαγράφηκε το {{deleteModelTag}}", "Deleted {{name}}": "Διαγράφηκε το {{name}}", "Deleted User": "Διαγράφηκε ο Χρήστης", "Deployment names are required for Azure OpenAI": "", "Describe Pictures in Documents": "", "Describe your knowledge base and objectives": "Περιγράψτε τη βάση γνώσης και τους στόχους σας", "Description": "Περιγραφή", "Detect Artifacts Automatically": "", "Dictate": "", "Didn't fully follow instructions": "Δεν ακολούθησε πλήρως τις οδηγίες", "Direct": "", "Direct Connections": "", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "", "Direct Tool Servers": "", "Disable Code Interpreter": "", "Disable Image Extraction": "", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "", "Disabled": "Απενεργοποιημένο", "Discover a function": "Ανακάλυψη λειτουργίας", "Discover a model": "Ανακάλυψη μοντέλου", "Discover a prompt": "Ανακάλυψη προτροπής", "Discover a tool": "Ανακάλυψη εργαλείου", "Discover how to use Open WebUI and seek support from the community.": "", "Discover wonders": "Ανακάλυψη θαυμάτων", "Discover, download, and explore custom functions": "Ανα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, κα<PERSON><PERSON>βάστε και εξερευνήστε προσαρμοσμένες λειτουργίες", "Discover, download, and explore custom prompts": "Ανα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, κα<PERSON><PERSON>βάστε και εξερευνήστε προσαρμοσμένες προτροπές", "Discover, download, and explore custom tools": "Ανα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, κατ<PERSON>βάστε και εξερευνήστε προσαρμοσμένα εργαλεία", "Discover, download, and explore model presets": "Ανα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, κατ<PERSON>βάστε και εξερευνήστε προκαθορισμένα μοντέλα", "Display": "Εμφάνιση", "Display Emoji in Call": "Εμφάνιση Emoji στην Κλήση", "Display the username instead of You in the Chat": "Εμφάνιση του ονόματος χρήστη αντί του Εσάς στη Συνομιλία", "Displays citations in the response": "Εμφανίζει αναφορές στην απάντηση", "Dive into knowledge": "Βυθιστείτε στη γνώση", "Do not install functions from sources you do not fully trust.": "Μην εγκαθιστάτε λειτουργίες από πηγές που δεν εμπιστεύεστε πλήρως.", "Do not install tools from sources you do not fully trust.": "Μην εγκαθιστάτε εργαλεία από πηγές που δεν εμπιστεύεστε πλήρως.", "Docling": "", "Docling Server URL required.": "", "Document": "Έγγραφο", "Document Intelligence": "", "Document Intelligence endpoint and key required.": "", "Documentation": "Τεκμηρίωση", "Documents": "Έγγραφα", "does not make any external connections, and your data stays securely on your locally hosted server.": "δεν κάνει καμία εξωτερική σύνδεση, και τα δεδομένα σας παραμένουν ασφαλή στον τοπικά φιλοξενούμενο διακομιστή σας.", "Domain Filter List": "", "Don't have an account?": "Δεν έχετε λογαριασμό;", "don't install random functions from sources you don't trust.": "μην εγκαθιστάτε τυχαίες λειτουργίες από πηγές που δεν εμπιστεύεστε.", "don't install random tools from sources you don't trust.": "μην εγκαθιστάτε τυχαία εργαλεία από πηγές που δεν εμπιστεύεστε.", "Don't like the style": "Δεν σας αρέσει το στυλ", "Done": "Έτοιμο", "Download": "Λή<PERSON>η", "Download as SVG": "", "Download canceled": "Η λήψη ακυρώθηκε", "Download Database": "Λήψη Βάσης Δεδομένων", "Drag and drop a file to upload or select a file to view": "Σύρετε και αφήστε ένα αρχείο για να το ανεβάσετε ή επιλέξτε ένα αρχείο για να το δείτε", "Draw": "Σχεδίαση", "Drop any files here to upload": "", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "π.χ. '30s','10m'. Οι έγκυρες μονάδες χρόνου είναι 's', 'm', 'h'.", "e.g. \"json\" or a JSON schema": "", "e.g. 60": "", "e.g. A filter to remove profanity from text": "π.χ. Ένα φίλτρο για να αφαιρέσετε βρισιές από το κείμενο", "e.g. en": "", "e.g. My Filter": "π.χ. Το Φίλτρου Μου", "e.g. My Tools": "π.χ. Τα Εργαλεία Μου", "e.g. my_filter": "π.χ. my_filter", "e.g. my_tools": "π.χ. my_tools", "e.g. pdf, docx, txt": "", "e.g. Tools for performing various operations": "π.χ. Εργαλεία για την εκτέλεση διάφορων λειτουργιών", "e.g., 3, 4, 5 (leave blank for default)": "", "e.g., audio/wav,audio/mpeg,video/* (leave blank for defaults, * for all)": "", "e.g., en-US,ja-JP (leave blank for auto-detect)": "", "e.g., westus (leave blank for eastus)": "", "e.g.) en,fr,de": "", "Edit": "Επεξεργασία", "Edit Arena Model": "Επεξεργασία Μοντέλου Arena", "Edit Channel": "", "Edit Connection": "Επεξεργασία Σύνδεσης", "Edit Default Permissions": "Επεξεργασία Προεπιλεγμένων Δικαιωμάτων", "Edit Folder": "", "Edit Memory": "Επεξεργασία Μνήμης", "Edit User": "Επεξεργασία Χρήστη", "Edit User Group": "Επεξεργασ<PERSON>α Ομάδα<PERSON>ών", "Edited": "", "Editing": "", "Eject": "", "ElevenLabs": "ElevenLabs", "Email": "Email", "Embark on adventures": "Ξεκινήστε περιπέτειες", "Embedding": "", "Embedding Batch Size": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ρτί<PERSON><PERSON>ς Ενσωμάτωσης", "Embedding Model": "Μοντ<PERSON><PERSON><PERSON> Ενσωμάτωσης", "Embedding Model Engine": "Μηχανή Μοντέλου Ενσωμάτωσης", "Embedding model set to \"{{embedding_model}}\"": "Το μοντέλο ενσωμάτωσης έχει οριστεί σε \"{{embedding_model}}\"", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Code Execution": "", "Enable Code Interpreter": "", "Enable Community Sharing": "Ενεργοποίηση Κοινοτι<PERSON><PERSON><PERSON> Κοινής Χρήσης", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "Ενεργοποίηση Κλείδωσης Μνήμης (mlock) για την αποτροπή της ανταλλαγής δεδομένων του μοντέλου από τη μνήμη RAM. Αυτή η επιλογή κλειδώνει το σύνολο εργασίας των σελίδων του μοντέλου στη μνήμη RAM, διασφαλίζοντας ότι δεν θα ανταλλαχθούν στο δίσκο. Αυτ<PERSON> μπορεί να βοηθήσει στη διατήρηση της απόδοσης αποφεύγοντας σφάλματα σελίδων και διασφαλίζοντας γρήγορη πρόσβαση στα δεδομένα.", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "Ενεργοποίηση Χαρτογράφησης Μνήμης (mmap) για φόρτωση δεδομένων μοντέλου. Αυτή η επιλογή επιτρέπει στο σύστημα να χρησιμοποιεί αποθήκευση δίσκου ως επέκταση της μνήμης RAM, αντιμετωπίζοντας αρχεία δίσκου σαν να ήταν στη μνήμη RAM. Αυτό μπορεί να βελτιώσει την απόδοση του μοντέλου επιτρέποντας γρηγορότερη πρόσβαση στα δεδομένα. Ω<PERSON>τ<PERSON><PERSON><PERSON>, μπορεί να μην λειτουργεί σωστά με όλα τα συστήματα και να καταναλώνει σημαντικό χώρο στο δίσκο.", "Enable Message Rating": "Ενεργοποίηση Αξιολόγησης Μηνυμάτων", "Enable Mirostat sampling for controlling perplexity.": "", "Enable New Sign Ups": "Ενεργοποίηση Νέων Εγγραφών", "Enabled": "Ενεργοποιημένο", "Endpoint URL": "", "Enforce Temporary Chat": "", "Enhance": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "Βεβαιωθείτε ότι το αρχείο CSV σας περιλαμβάνει 4 στήλες με αυτή τη σειρά: Όνομα, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Ρόλος.", "Enter {{role}} message here": "Εισάγετε το μήνυμα {{role}} εδώ", "Enter a detail about yourself for your LLMs to recall": "Εισάγετε μια λεπτομέρεια για τον εαυτό σας ώστε τα LLMs να την ανακαλούν", "Enter a title for the pending user info overlay. Leave empty for default.": "", "Enter a watermark for the response. Leave empty for none.": "", "Enter api auth string (e.g. username:password)": "Εισάγετε τη σειρά επαλήθευσης api (π.χ. username:password)", "Enter Application DN": "Εισάγετε DN Εφαρμογής", "Enter Application DN Password": "Εισάγετε Κωδικό DN Εφαρμογής", "Enter Bing Search V7 Endpoint": "Εισάγετε το Τέλος Bing Search V7", "Enter Bing Search V7 Subscription Key": "Εισάγετε το Κλειδί Συνδρομής Bing Search V7", "Enter BM25 Weight": "", "Enter Bocha Search API Key": "", "Enter Brave Search API Key": "Εισάγετε το Κλειδί API Brave Search", "Enter certificate path": "Εισάγετε τη διαδρομή πιστοποιητικού", "Enter CFG Scale (e.g. 7.0)": "Εισάγετε το CFG Scale (π.χ. 7.0)", "Enter Chunk Overlap": "Εισάγετε την Επικάλυψη Τμημάτων", "Enter Chunk Size": "Εισάγετε το Μέγεθος Τμημάτων", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "", "Enter Config in JSON format": "", "Enter content for the pending user info overlay. Leave empty for default.": "", "Enter Datalab Marker API Key": "", "Enter description": "Εισάγετε την περιγραφή", "Enter Docling OCR Engine": "", "Enter Docling OCR Language(s)": "", "Enter Docling Server URL": "", "Enter Document Intelligence Endpoint": "", "Enter Document Intelligence Key": "", "Enter domains separated by commas (e.g., example.com,site.org)": "", "Enter Exa API Key": "", "Enter External Document Loader API Key": "", "Enter External Document Loader URL": "", "Enter External Web Loader API Key": "", "Enter External Web Loader URL": "", "Enter External Web Search API Key": "", "Enter External Web Search URL": "", "Enter Firecrawl API Base URL": "", "Enter Firecrawl API Key": "", "Enter folder name": "", "Enter Github Raw URL": "Εισάγετε το Github Raw URL", "Enter Google PSE API Key": "Εισάγετε το Κλειδί API Google PSE", "Enter Google PSE Engine Id": "Εισάγετε το Αναγνωριστικ<PERSON>ηχανής Google PSE", "Enter Image Size (e.g. 512x512)": "Εισάγετε το Μέγεθος <PERSON> (π.χ. 512x512)", "Enter Jina API Key": "Εισάγετε το Κλειδί API Jina", "Enter Jupyter Password": "", "Enter Jupyter Token": "", "Enter Jupyter URL": "", "Enter Kagi Search API Key": "", "Enter Key Behavior": "", "Enter language codes": "Εισάγετε κωδικούς γλώσσας", "Enter Mistral API Key": "", "Enter Model ID": "Εισάγετε το ID Μοντέλου", "Enter model tag (e.g. {{modelTag}})": "Εισάγετε την ετικέτα μοντέλου (π.χ. {{modelTag}})", "Enter Mojeek Search API Key": "Εισάγετε το Κλειδί API Mojeek Search", "Enter name": "", "Enter New Password": "", "Enter Number of Steps (e.g. 50)": "Εισάγετε τον Αριθμό Βημάτων (π.χ. 50)", "Enter Perplexity API Key": "", "Enter Playwright Timeout": "", "Enter Playwright WebSocket URL": "", "Enter proxy URL (e.g. **************************:port)": "", "Enter reasoning effort": "", "Enter Sampler (e.g. Euler a)": "Εισάγετε τον <PERSON> (π.χ. E<PERSON>r a)", "Enter Scheduler (e.g. Karras)": "Εισάγετε τον Scheduler (π.<PERSON><PERSON>)", "Enter Score": "Εισάγετε το Score", "Enter SearchApi API Key": "Εισάγετε το Κλειδί API SearchApi", "Enter SearchApi Engine": "Εισάγετε τη Μηχανή SearchApi", "Enter Searxng Query URL": "Εισάγετε το URL Ερώτησης Searxng", "Enter Seed": "Εισάγετε το Seed", "Enter SerpApi API Key": "", "Enter SerpApi Engine": "", "Enter Serper API Key": "Εισάγετε το Κλειδί API Serper", "Enter Serply API Key": "Εισάγετε το Κλειδί API Serply", "Enter Serpstack API Key": "Εισάγετε το Κλειδί API Serpstack", "Enter server host": "Εισάγετε τον διακομιστή host", "Enter server label": "Εισάγετε την ετικέτα διακομιστή", "Enter server port": "Εισάγετε την θύρα διακομιστή", "Enter Sougou Search API sID": "", "Enter Sougou Search API SK": "", "Enter stop sequence": "Εισάγετε τη σειρά παύσης", "Enter system prompt": "Εισάγετε την προτροπή συστήματος", "Enter system prompt here": "", "Enter Tavily API Key": "Εισάγετε το Κλειδί API <PERSON>", "Enter Tavily Extract Depth": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter the URL of the function to import": "", "Enter the URL to import": "", "Enter Tika Server URL": "Εισάγετε το URL διακομιστή Tika", "Enter timeout in seconds": "", "Enter to Send": "", "Enter Top K": "Εισάγετε το Top K", "Enter Top K Reranker": "", "Enter URL (e.g. http://127.0.0.1:7860/)": "Εισάγετε το URL (π.χ. http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "Εισάγετε το URL (π.χ. http://localhost:11434)", "Enter Yacy Password": "", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "", "Enter Yacy Username": "", "Enter your current password": "", "Enter Your Email": "Εισάγετε το Email σας", "Enter Your Full Name": "Εισάγετε το Πλήρες Όνομά σας", "Enter your message": "Εισάγετε το μήνυμά σας", "Enter your name": "", "Enter Your Name": "", "Enter your new password": "", "Enter Your Password": "Εισάγετε τον Κωδικό σας", "Enter Your Role": "Εισάγετε τον Ρόλο σας", "Enter Your Username": "Εισάγετε το Όνομα Χρήστη σας", "Enter your webhook URL": "", "Error": "Σφάλμα", "ERROR": "ΣΦΑΛΜΑ", "Error accessing Google Drive: {{error}}": "", "Error accessing media devices.": "", "Error starting recording.": "", "Error unloading model: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "Αξιολογήσεις", "Everyone": "", "Exa API Key": "", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "Παράδειγμα: (&(objectClass=inetOrgPerson)(uid=%s))", "Example: ALL": "Παράδειγμα: ALL", "Example: mail": "", "Example: ou=users,dc=foo,dc=example": "Παράδειγμα: ou=users,dc=foo,dc=example", "Example: sAMAccountName or uid or userPrincipalName": "Παράδειγμα: sAMAccountName ή uid ή userPrincipalName", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "", "Exclude": "Εξαίρεση", "Execute code for analysis": "", "Executing **{{NAME}}**...": "", "Expand": "", "Experimental": "Πειραματικό", "Explain": "", "Explore the cosmos": "Εξερευνήστε το σύμπαν", "Export": "Εξαγωγή", "Export All Archived Chats": "Εξαγωγή Όλων των Αρχειοθετημένων Συνομιλιών", "Export All Chats (All Users)": "Εξαγωγή Όλων των Συνομιλιών (Όλοι οι Χρήστες)", "Export chat (.json)": "Εξαγωγή συνομιλίας (.json)", "Export Chats": "Εξαγωγή Συνομιλιών", "Export Config to JSON File": "Εξαγωγή Διαμόρφωσης σε Αρχείο JSON", "Export Functions": "Εξαγωγή Λειτουργιών", "Export Models": "Εξαγωγ<PERSON>ων", "Export Presets": "Εξαγωγή Προκαθορισμένων", "Export Prompt Suggestions": "", "Export Prompts": "Εξαγωγή Προτροπών", "Export to CSV": "Εξαγωγή σε CSV", "Export Tools": "Εξαγωγή Εργαλείων", "External": "", "External Document Loader URL required.": "", "External Task Model": "", "External Web Loader API Key": "", "External Web Loader URL": "", "External Web Search API Key": "", "External Web Search URL": "", "Failed to add file.": "Αποτυχία προσθήκης αρχείου.", "Failed to connect to {{URL}} OpenAPI tool server": "", "Failed to copy link": "", "Failed to create API Key.": "Αποτυχία δημιουργίας Κλειδιού API.", "Failed to delete note": "", "Failed to extract content from the file: {{error}}": "", "Failed to extract content from the file.": "", "Failed to fetch models": "", "Failed to generate title": "", "Failed to load file content.": "", "Failed to read clipboard contents": "Αποτυχία ανάγνωσης περιεχομένων πρόχειρου", "Failed to save connections": "", "Failed to save models configuration": "Αποτυχία αποθήκευσης ρυθμίσεων μοντέλων", "Failed to update settings": "Αποτυχία ενημέρωσης ρυθμίσεων", "Failed to upload file.": "Αποτυχία ανεβάσματος αρχείου.", "Features": "", "Features Permissions": "", "February": "Φεβρουά<PERSON>ιος", "Feedback Details": "", "Feedback History": "Ιστορι<PERSON><PERSON> Ανατροφοδότησης", "Feedbacks": "Ανατροφοδοτήσεις", "Feel free to add specific details": "Νιώστε ελεύθεροι να προσθέσετε συγκεκριμένες λεπτομέρειες", "File": "Αρχείο", "File added successfully.": "Το αρχείο προστέθηκε με επιτυχία.", "File content updated successfully.": "Το περιεχόμενο του αρχείου ενημερώθηκε με επιτυχία.", "File Mode": "Λειτουργία Αρχείου", "File not found.": "Αρχεί<PERSON> δεν βρέθηκε.", "File removed successfully.": "Το αρχείο αφαιρέθηκε με επιτυχία.", "File size should not exceed {{maxSize}} MB.": "Το μέγεθος του αρχείου δεν πρέπει να υπερβαίνει τα {{maxSize}} MB.", "File Upload": "", "File uploaded successfully": "", "Files": "Αρχεία", "Filter": "", "Filter is now globally disabled": "Το φίλτρο είναι τώρα καθολικά απενεργοποιημένο", "Filter is now globally enabled": "Το φίλτρο είναι τώρα καθολικά ενεργοποιημένο", "Filters": "Φίλτρα", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "Εντοπίστηκε spoofing δακτυλικού αποτυπώματος: Αδυναμ<PERSON>α χρήσης αρχικών ως avatar. Χρήση της προεπιλεγμένης εικόνας προφίλ.", "Firecrawl API Base URL": "", "Firecrawl API Key": "", "Fluidly stream large external response chunks": "Ροή μεγάλων εξωτερικών τμημάτων απάντησης ομαλά", "Focus chat input": "Εστίαση στο πεδίο συνομιλίας", "Folder deleted successfully": "Ο φάκελος διαγράφηκε με επιτυχία", "Folder Name": "", "Folder name cannot be empty.": "Το όνομα του φακέλου δεν μπορεί να είναι κενό.", "Folder name updated successfully": "Το όνομα του φακέλου ενημερώθηκε με επιτυχία", "Folder updated successfully": "", "Follow up": "", "Follow Up Generation": "", "Follow Up Generation Prompt": "", "Follow-Up Auto-Generation": "", "Followed instructions perfectly": "Ακολούθησε τις οδηγίες τέλεια", "Force OCR": "", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "", "Forge new paths": "Δημιουργήστε νέες διαδρομές", "Form": "Φόρμα", "Format your variables using brackets like this:": "Μορφοποιήστε τις μεταβλητές σας χρησιμοποιώντας αγκύλες όπως αυτό:", "Forwards system user session credentials to authenticate": "", "Full Context Mode": "", "Function": "Λειτουργία", "Function Calling": "", "Function created successfully": "Η λειτουργία δημιουργήθηκε με επιτυχία", "Function deleted successfully": "Η λειτουργία διαγράφηκε με επιτυχία", "Function Description": "Περιγρα<PERSON>ή Λειτουργίας", "Function ID": "ID Λειτουργίας", "Function imported successfully": "", "Function is now globally disabled": "Η λειτουργία είναι τώρα καθολικά απενεργοποιημένη", "Function is now globally enabled": "Η λειτουργία είναι τώρα καθολικά ενεργοποιημένη", "Function Name": "Όνομα Λειτουργίας", "Function updated successfully": "Η λειτουργία ενημερώθηκε με επιτυχία", "Functions": "Λειτουργ<PERSON>ες", "Functions allow arbitrary code execution.": "Οι λειτουργίες επιτρέπουν την εκτέλεση αυθαίρετου κώδικα.", "Functions imported successfully": "Οι λειτουργίες εισήχθησαν με επιτυχία", "Gemini": "", "Gemini API Config": "", "Gemini API Key is required.": "", "General": "Γενικά", "Generate": "", "Generate an image": "", "Generate Image": "Δημιουργία Εικόνας", "Generate prompt pair": "", "Generating search query": "Γ<PERSON><PERSON><PERSON><PERSON> αναζήτη<PERSON>ης ερώτησης", "Generating...": "", "Get information on {{name}} in the UI": "", "Get started": "Ξεκινήστε", "Get started with {{WEBUI_NAME}}": "Ξεκινήστε με {{WEBUI_NAME}}", "Global": "Καθολικό", "Good Response": "Καλή Απάντηση", "Google Drive": "", "Google PSE API Key": "Κλειδί API Google PSE", "Google PSE Engine Id": "Αναγνωριστικό Μηχανής Google PSE", "Group created successfully": "Η ομάδα δημιουργήθηκε με επιτυχία", "Group deleted successfully": "Η ομάδα διαγράφηκε με επιτυχία", "Group Description": "Περιγρα<PERSON>ή Ομάδας", "Group Name": "Όνομα Ομάδας", "Group updated successfully": "Η ομάδα ενημερώθηκε με επιτυχία", "Groups": "Ομάδες", "H1": "", "H2": "", "H3": "", "Haptic Feedback": "Ανατροφοδότη<PERSON>η Haptic", "Hello, {{name}}": "Γειά σου, {{name}}", "Help": "Βοήθεια", "Help us create the best community leaderboard by sharing your feedback history!": "Βοηθήστε μας να δημιουργήσουμε την καλύτερη κατάταξη κοινότητας μοιράζοντας το ιστορικό ανατροφοδότησής σας!", "Hex Color": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Hex Color - Leave empty for default color": "Χρώμα Hex - Αφήστε κενό για προεπιλεγμένο χρώμα", "Hide": "Απόκρυψη", "Hide from Sidebar": "", "Hide Model": "", "High Contrast Mode": "", "Home": "", "Host": "Διακομιστής", "How can I help you today?": "<PERSON><PERSON><PERSON> μπορώ να σας βοηθήσω σήμερα;", "How would you rate this response?": "Πώς θα βαθμολογούσατε αυτή την απάντηση;", "HTML": "", "Hybrid Search": "Υβριδική Αναζήτηση", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "Αναγν<PERSON><PERSON><PERSON><PERSON><PERSON> ότι έχω διαβάσει και κατανοώ τις συνέπειες της ενέργειάς μου. Γνωρίζω τους κινδύνους που σχετίζονται με την εκτέλεση αυθαίρετου κώδικα και έχω επαληθεύσει την αξιοπιστία της πηγής.", "ID": "ID", "iframe Sandbox Allow Forms": "", "iframe Sandbox Allow Same Origin": "", "Ignite curiosity": "Ξύπνημα της περιέργειας", "Image": "", "Image Compression": "", "Image Compression Height": "", "Image Compression Width": "", "Image Generation": "", "Image Generation (Experimental)": "Δημιουργ<PERSON><PERSON> (Πειραματικό)", "Image Generation Engine": "Μηχανή Δημιουρ<PERSON><PERSON><PERSON>ς Εικόνας", "Image Max Compression Size": "", "Image Max Compression Size height": "", "Image Max Compression Size width": "", "Image Prompt Generation": "", "Image Prompt Generation Prompt": "", "Image Settings": "Ρυθμίσεις Εικόνας", "Images": "Εικόνες", "Import": "", "Import Chats": "Εισαγωγ<PERSON>υνομ<PERSON>λιών", "Import Config from JSON File": "Εισαγωγή Διαμόρφωσης από Αρχείο JSON", "Import From Link": "", "Import Functions": "Εισαγωγ<PERSON> Λειτουργιών", "Import Models": "Εισαγωγ<PERSON>ων", "Import Notes": "", "Import Presets": "Εισαγωγή Προκαθορισμένων", "Import Prompt Suggestions": "", "Import Prompts": "Εισαγωγή Προτροπών", "Import Tools": "Εισαγωγή Εργαλείων", "Include": "Συμπερίληψη", "Include `--api-auth` flag when running stable-diffusion-webui": "Συμπεριλάβετε το flag `--api-auth` όταν τρέχετε το stable-diffusion-webui", "Include `--api` flag when running stable-diffusion-webui": "Συμπεριλάβετε το flag `--api` όταν τρέχετε το stable-diffusion-webui", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "", "Info": "Πληροφορίες", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "", "Input commands": "Εισαγωγ<PERSON> εντολών", "Input Variables": "", "Insert": "", "Insert Prompt as Rich Text": "", "Install from Github URL": "Εγκατάσταση από URL Github", "Instant Auto-Send After Voice Transcription": "Άμεση Αυτόματη Αποστολή μετά τη μεταγραφή φωνής", "Integration": "", "Interface": "Διεπαφή", "Invalid file content": "", "Invalid file format.": "Μη έγκυρη μορφή αρχείου.", "Invalid JSON file": "", "Invalid Tag": "Μη έγκυρη Ετικέτα", "is typing...": "", "Italic": "", "January": "Ιανου<PERSON><PERSON>ιος", "Jina API Key": "Κλειδί API Jina", "join our Discord for help.": "συμμετέχετε στο Discord μας για βοήθεια.", "JSON": "JSON", "JSON Preview": "Προεπισκόπηση JSON", "July": "<PERSON>ού<PERSON>ιος", "June": "Ιούνιος", "Jupyter Auth": "", "Jupyter URL": "", "JWT Expiration": "Λήξη JWT", "JWT Token": "Token JWT", "Kagi Search API Key": "", "Keep in Sidebar": "", "Key": "Κλειδί", "Keyboard shortcuts": "Συντομ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Πληκτρολογίου", "Knowledge": "Γνώση", "Knowledge Access": "Πρόσβαση στη Γνώση", "Knowledge Base": "", "Knowledge created successfully.": "Η γνώση δημιουργήθηκε με επιτυχία.", "Knowledge deleted successfully.": "Η γνώση διαγράφηκε με επιτυχία.", "Knowledge Public Sharing": "", "Knowledge reset successfully.": "Η γνώση επαναφέρθηκε με επιτυχία.", "Knowledge updated successfully": "Η γνώση ενημερώθηκε με επιτυχία", "Kokoro.js (Browser)": "", "Kokoro.js Dtype": "", "Label": "Ετικέτα", "Landing Page Mode": "Λειτουργία Σελίδας Άφιξης", "Language": "Γλώσσα", "Language Locales": "", "Languages": "", "Last Active": "Τελευταία Ενεργή", "Last Modified": "Τελευταία Τροποποίηση", "Last reply": "", "LDAP": "LDAP", "LDAP server updated": "Ο διακομιστής LDAP ενημερώθηκε", "Leaderboard": "Κατάταξη", "Learn more about OpenAPI tool servers.": "", "Leave empty for no compression": "", "Leave empty for unlimited": "Αφήστε κενό για απεριόριστο", "Leave empty to include all models from \"{{url}}\" endpoint": "", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{url}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "Αφήστε κενό για να χρησιμοποιήσετε όλα τα μοντέλα ή επιλέξτε συγκεκριμένα μοντέλα", "Leave empty to use the default prompt, or enter a custom prompt": "Αφήστε κενό για να χρησιμοποιήσετε την προεπιλεγμένη προτροπή, ή εισάγετε μια προσαρμοσμένη προτροπή", "Leave model field empty to use the default model.": "", "License": "", "Light": "Φως", "Listening...": "Ακούγεται...", "Llama.cpp": "", "LLMs can make mistakes. Verify important information.": "Τα LLM μπορούν να κάνουν λάθη. Επαληθεύστε σημαντικές πληροφορίες.", "Loader": "", "Loading Kokoro.js...": "", "Local": "Τοπικό", "Local Task Model": "", "Location access not allowed": "", "Lost": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LTR": "LTR", "Made by Open WebUI Community": "Δημιουργήθηκε από την Κοινότητα OpenWebUI", "Make password visible in the user interface": "", "Make sure to enclose them with": "Βεβαιωθείτε ότι τα περικλείετε με", "Make sure to export a workflow.json file as API format from ComfyUI.": "Βεβαιωθείτε ότι εξάγετε ένα αρχείο workflow.json ως μορφή API από το ComfyUI.", "Manage": "Διαχείριση", "Manage Direct Connections": "", "Manage Models": "", "Manage Ollama": "Διαχείρι<PERSON>η Ollama", "Manage Ollama API Connections": "Διαχείριση Συνδέσεων API Ollama", "Manage OpenAI API Connections": "Διαχείριση Συνδέσεων API OpenAI", "Manage Pipelines": "Διαχείρι<PERSON>η Καναλιών", "Manage Tool Servers": "", "March": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Markdown": "", "Markdown (Header)": "", "Max Speakers": "", "Max Upload Count": "Μέγιστος Αριθμός Ανεβάσματος", "Max Upload Size": "Μέγιστο Μέγεθος Αρχείου", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "Μέγιστο των 3 μοντέλων μπορούν να κατεβούν ταυτόχρονα. Παρακαλ<PERSON> δοκιμάστε ξανά αργότερα.", "May": "<PERSON><PERSON><PERSON><PERSON>", "Memories accessible by LLMs will be shown here.": "Οι αναμνήσεις προσβάσιμες από τα LLMs θα εμφανιστούν εδώ.", "Memory": "Μνήμη", "Memory added successfully": "Η μνήμη προστέθηκε με επιτυχία", "Memory cleared successfully": "Η μνήμη καθαρίστηκε με επιτυχία", "Memory deleted successfully": "Η μνήμη διαγράφηκε με επιτυχία", "Memory updated successfully": "Η μνήμη ενημερώθηκε με επιτυχία", "Merge Responses": "Συγχώνευση Απαντήσεων", "Merged Response": "Συγχωνευμένη απάντηση", "Message rating should be enabled to use this feature": "Η αξιολόγηση μηνυμάτων πρέπει να είναι ενεργοποιημένη για να χρησιμοποιήσετε αυτή τη λειτουργία", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "Τα μηνύματα που στέλνετε μετά τη δημιουργία του συνδέσμου σας δεν θα κοινοποιηθούν. Οι χρήστες με το URL θα μπορούν να δουν τη συνομιλία που μοιραστήκατε.", "Microsoft OneDrive": "", "Microsoft OneDrive (personal)": "", "Microsoft OneDrive (work/school)": "", "Mistral OCR": "", "Mistral OCR API Key required.": "", "Model": "<PERSON>ο<PERSON><PERSON><PERSON><PERSON><PERSON>", "Model '{{modelName}}' has been successfully downloaded.": "Το μοντέλο '{{modelName}}' κατεβάστηκε με επιτυχία.", "Model '{{modelTag}}' is already in queue for downloading.": "Το μοντέλο '{{modelTag}}' βρίσ<PERSON><PERSON>ται ήδη στην ουρά για λήψη.", "Model {{modelId}} not found": "Το μοντέλο {{modelId}} δεν βρέθηκε", "Model {{modelName}} is not vision capable": "Το μοντέλο {{modelName}} δεν έχει δυνατότητα όρασης", "Model {{name}} is now {{status}}": "Το μοντέλο {{name}} είναι τώρα {{status}}", "Model {{name}} is now hidden": "", "Model {{name}} is now visible": "", "Model accepts file inputs": "", "Model accepts image inputs": "Το μοντέλο δέχεται είσοδο εικόνας", "Model can execute code and perform calculations": "", "Model can generate images based on text prompts": "", "Model can search the web for information": "", "Model created successfully!": "Το μοντέλο δημιουργήθηκε με επιτυχία!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "Ανιχνεύθηκε διαδρομή αρχείου μοντέλου. Το σύντομο όνομα μοντέλου απαιτείται για ενημέρωση, δεν μπορεί να συνεχιστεί.", "Model Filtering": "Φιλτράρισμα Μοντέλων", "Model ID": "ID Μοντέλου", "Model IDs": "IDs Μοντέλων", "Model Name": "Όνομα Μοντέλου", "Model not selected": "Το μοντέλο δεν έχει επιλεγεί", "Model Params": "Παράμετροι Μοντέλου", "Model Permissions": "Δι<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>έλου", "Model unloaded successfully": "", "Model updated successfully": "Το μοντέλο ενημερώθηκε με επιτυχία", "Model(s) do not support file upload": "", "Modelfile Content": "Περιεχόμενο Αρχείου Μοντέλου", "Models": "Μοντ<PERSON>λα", "Models Access": "Πρόσβαση Μοντέλων", "Models configuration saved successfully": "Η διαμόρφωση των μοντέλων αποθηκεύτηκε με επιτυχία", "Models Public Sharing": "", "Mojeek Search API Key": "Κλειδί API Mojeek Search", "more": "περισσότερα", "More": "Περισσότερα", "Name": "Όνομα", "Name your knowledge base": "Ονομάστε τη βάση γνώσης σας", "Native": "", "New Chat": "Νέα Συνομιλία", "New Folder": "", "New Function": "", "New Note": "", "New Password": "<PERSON><PERSON><PERSON>", "New Tool": "", "new-channel": "", "Next message": "", "No chats found for this user.": "", "No chats found.": "", "No content": "", "No content found": "Δεν βρέθηκε περιεχόμενο", "No content found in file.": "", "No content to speak": "Δεν υπάρχει περιεχόμενο για ανάγνωση", "No distance available": "Δεν υπάρχει διαθέσιμη απόσταση", "No feedbacks found": "Δεν βρέθηκαν ανατροφοδοτήσεις", "No file selected": "Δεν έχει επιλεγεί αρχείο", "No groups with access, add a group to grant access": "Δεν υπάρχουν ομάδες με πρόσβαση, προσθέστε μια ομάδα για να χορηγήσετε πρόσβαση", "No HTML, CSS, or JavaScript content found.": "Δεν βρέθηκε περιεχόμενο HTML, CSS ή JavaScript.", "No inference engine with management support found": "", "No knowledge found": "Δεν βρέθηκε γνώση", "No memories to clear": "", "No model IDs": "Δεν υπάρχουν IDs μοντέλων", "No models found": "Δεν βρέθηκαν μοντέλα", "No models selected": "Δεν έχουν επιλεγεί μοντέλα", "No Notes": "", "No results found": "Δεν βρέθηκαν αποτελέσματα", "No search query generated": "Δεν δημιουργήθηκε ερώτηση αναζήτησης", "No source available": "Δεν υπάρχει διαθέσιμη πηγή", "No users were found.": "Δεν βρέθηκαν χρήστες.", "No valves to update": "Δεν υπάρχουν βαλβίδες για ενημέρωση", "None": "Κανένα", "Not factually correct": "Δεν είναι γεγονότα", "Not helpful": "Δεν είναι χρήσιμο", "Note deleted successfully": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "Σημείωση: Αν ορίσετε ένα ελάχιστο score, η αναζήτηση θα επιστρέψει μόνο έγγραφα με score μεγαλύτερο ή ίσο με το ελάχιστο score.", "Notes": "Σημειώσεις", "Notification Sound": "", "Notification Webhook": "", "Notifications": "Ειδοποιήσεις", "November": "Νοέμβριος", "OAuth ID": "OAuth ID", "October": "Οκτώβριος", "Off": "Off", "Okay, Let's Go!": "Εντάξει, Πάμε!", "OLED Dark": "Σκούρο OLED", "Ollama": "Ollama", "Ollama API": "API Ollama", "Ollama API settings updated": "Οι ρυθμίσεις API Ollama ενημερώθηκαν", "Ollama Version": "Έκδοση <PERSON><PERSON>ma", "On": "On", "OneDrive": "", "Only active when \"Paste Large Text as File\" setting is toggled on.": "", "Only active when the chat input is in focus and an LLM is generating a response.": "", "Only alphanumeric characters and hyphens are allowed": "Επιτρέποντα<PERSON> μόνο αλφαριθμητικοί χαρακτήρες και παύλες", "Only alphanumeric characters and hyphens are allowed in the command string.": "Επιτρέπονται μόνο αλφαριθμητικοί χαρακτήρες και παύλες στο string της εντολής.", "Only collections can be edited, create a new knowledge base to edit/add documents.": "Μόν<PERSON> συλλογές μπορούν να επεξεργαστούν, δημιουργήστε μια νέα βάση γνώσης για επεξεργασία/προσθήκη εγγράφων.", "Only markdown files are allowed": "", "Only select users and groups with permission can access": "Μόνο επιλεγμένοι χρήστες και ομάδες με άδεια μπορούν να έχουν πρόσβαση", "Oops! Looks like the URL is invalid. Please double-check and try again.": "Ωχ! Φαίνεται ότι το URL είναι μη έγκυρο. Παρακ<PERSON>λ<PERSON> ελέγξτε ξανά και δοκιμάστε.", "Oops! There are files still uploading. Please wait for the upload to complete.": "Ωχ! Υπάρχουν αρχεία που εξακολουθούν να ανεβαίνουν. Παρακαλώ περιμένετε να ολοκληρωθεί η μεταφόρτωση.", "Oops! There was an error in the previous response.": "Ωχ! Υπήρξε σφάλμα στην προηγούμενη απάντηση.", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "Ωχ! Χρησιμοποιείτε μια μη υποστηριζόμενη μέθοδο (μόνο frontend). <PERSON>α<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> σερβίρετε το WebUI από το backend.", "Open file": "Άνοιγμα αρχείου", "Open in full screen": "Άνοιγμα σε πλήρη οθόνη", "Open modal to configure connection": "", "Open new chat": "Άνοιγμα νέας συνομιλίας", "Open WebUI can use tools provided by any OpenAPI server.": "", "Open WebUI uses faster-whisper internally.": "Το Open WebUI χρησιμοποιεί το faster-whisper εσωτερικά.", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "Το Open WebUI χρησιμοποιεί τα SpeechT5 και CMU Arctic embeddings ομιλητών.", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "Η έκδοση Open WebUI (v{{OPEN_WEBUI_VERSION}}) είναι χαμηλότερη από την απαιτούμενη έκδοση (v{{REQUIRED_VERSION}})", "OpenAI": "OpenAI", "OpenAI API": "API OpenAI", "OpenAI API Config": "Διαμόρφωση API OpenAI", "OpenAI API Key is required.": "Απαιτείται το Κλειδί API OpenAI.", "OpenAI API settings updated": "Οι ρυθμίσεις API OpenAI ενημερώθηκαν", "OpenAI URL/Key required.": "Απαιτείται URL/Kλειδί OpenAI.", "openapi.json URL or Path": "", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "", "or": "ή", "Ordered List": "", "Organize your users": "Οργανώστε τους χρήστες σας", "Other": "Άλλο", "OUTPUT": "ΕΞΟΔΟΣ", "Output format": "Μορφή εξόδου", "Output Format": "", "Overview": "Επισκόπηση", "page": "σελίδα", "Paginate": "", "Parameters": "", "Password": "Κω<PERSON>ικ<PERSON>ς", "Paste Large Text as File": "Επικόλληση Μεγάλου Κειμένου ως Αρχείο", "PDF document (.pdf)": "Έγγραφο PDF (.pdf)", "PDF Extract Images (OCR)": "Εξαγωγή Εικόνων PDF (OCR)", "pending": "εκκρεμεί", "Pending": "", "Pending User Overlay Content": "", "Pending User Overlay Title": "", "Permission denied when accessing media devices": "Άρνηση δικαιώμα<PERSON>ος κατά την πρόσβαση σε μέσα συσκευές", "Permission denied when accessing microphone": "Άρνηση δικαιώματος κατά την πρόσβαση σε μικρόφωνο", "Permission denied when accessing microphone: {{error}}": "Άρνηση δικαιώματος κατά την πρόσβαση σε μικρόφωνο: {{error}}", "Permissions": "Δικαιώματα", "Perplexity API Key": "", "Perplexity Model": "", "Perplexity Search Context Usage": "", "Personalization": "Προσωποποίηση", "Picture Description API Config": "", "Picture Description Local Config": "", "Picture Description Mode": "", "Pin": "Καρφίτσωμα", "Pinned": "Καρφιτσωμένο", "Pioneer insights": "Συμβουλές πρωτοπόρων", "Pipe": "", "Pipeline deleted successfully": "Η συνάρτηση διαγράφηκε με επιτυχία", "Pipeline downloaded successfully": "Η συνάρτηση κατεβλήθηκε με επιτυχία", "Pipelines": "Συναρτή<PERSON><PERSON>ις", "Pipelines Not Detected": "Συναρτή<PERSON><PERSON>ις Δεν Εντοπίστηκαν", "Pipelines Valves": "Βαλ<PERSON><PERSON><PERSON><PERSON><PERSON>υναρτήσεων", "Plain text (.md)": "", "Plain text (.txt)": "Απλό κείμενο (.txt)", "Playground": "Γ<PERSON><PERSON><PERSON><PERSON><PERSON> παιχνιδιών", "Playwright Timeout (ms)": "", "Playwright WebSocket URL": "", "Please carefully review the following warnings:": "Παρα<PERSON><PERSON><PERSON><PERSON> αναθεωρήστε προσεκτικά τις ακόλουθες προειδοποιήσεις:", "Please do not close the settings page while loading the model.": "", "Please enter a prompt": "Παρ<PERSON><PERSON><PERSON><PERSON><PERSON> εισάγετε μια προτροπή", "Please enter a valid path": "", "Please enter a valid URL": "", "Please fill in all fields.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> συμπληρώστε όλα τα πεδία.", "Please select a model first.": "", "Please select a model.": "", "Please select a reason": "Παρα<PERSON><PERSON><PERSON><PERSON> επιλέξτε έναν λόγο", "Port": "Θύρα", "Positive attitude": "Θετική στάση", "Prefix ID": "ID Προθέματος", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "Το ID Προθέματος χρησιμοποιείται για να αποφεύγονται συγκρούσεις με άλλες συνδέσεις προσθέτοντας ένα πρόθεμα στα IDs των μοντέλων - αφήστε κενό για απενεργοποίηση", "Prevent file creation": "", "Preview": "", "Previous 30 days": "Προηγούμενες 30 ημέρες", "Previous 7 days": "Προηγούμενες 7 ημέρες", "Previous message": "", "Private": "", "Profile Image": "Εικόνα Προφίλ", "Prompt": "", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "Προτροπή (π.χ. Πες μου ένα διασκεδαστικ<PERSON> γεγονός για την Ρωμαϊκή Αυτοκρατορία)", "Prompt Autocompletion": "", "Prompt Content": "Περιεχόμενο Προτροπής", "Prompt created successfully": "Η προτροπή δημιουργήθηκε με επιτυχία", "Prompt suggestions": "Προτάσ<PERSON>ις Προτροπής", "Prompt updated successfully": "Η προτροπή ενημερώθηκε με επιτυχία", "Prompts": "Προτροπ<PERSON>ς", "Prompts Access": "Πρόσβαση Προτροπών", "Prompts Public Sharing": "", "Public": "", "Pull \"{{searchValue}}\" from Ollama.com": "Τραβήξτε \"{{searchValue}}\" από το Ollama.com", "Pull a model from Ollama.com": "Τραβήξτε ένα μοντέλο από το Ollama.com", "Query Generation Prompt": "Προτροπή Δημιουργ<PERSON>ας Ερωτήσεων", "RAG Template": "Πρότυπο RAG", "Rating": "Βαθμολογία", "Re-rank models by topic similarity": "Επανατάξη μοντέλων κατά ομοιότητα θέματος", "Read": "", "Read Aloud": "Ανάγνωση Φωναχτά", "Reason": "", "Reasoning Effort": "", "Record": "", "Record voice": "Εγγραφή φωνής", "Redirecting you to Open WebUI Community": "Μετακατεύθυνση στην Κοινότητα OpenWebUI", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "Αναφέρεστε στον εαυτό σας ως \"User\" (π.χ., \"User μαθαίνει Ισπανικά\")", "References from": "Αναφορές από", "Refused when it shouldn't have": "Αρνήθηκε όταν δεν έπρεπε", "Regenerate": "Αναγεννήστε", "Reindex": "", "Reindex Knowledge Base Vectors": "", "Release Notes": "Σημειώσεις Έκδοσης", "Releases": "", "Relevance": "Σχετικότητα", "Relevance Threshold": "", "Remember Dismissal": "", "Remove": "Αφαίρεση", "Remove {{MODELID}} from list.": "", "Remove file": "", "Remove File": "", "Remove image": "", "Remove Model": "Αφαίρεση Μοντέλου", "Remove this tag from list": "", "Rename": "Μετονομασία", "Reorder Models": "Επανατ<PERSON>ξινόμηση Μοντέλων", "Reply in Thread": "", "Reranking Engine": "", "Reranking Model": "Μοντ<PERSON><PERSON><PERSON>ανα<PERSON>αξινόμησης", "Reset": "Επαναφορά", "Reset All Models": "Επαναφορά Όλων των Μοντέλων", "Reset Upload Directory": "Επαναφ<PERSON><PERSON><PERSON> Καταλόγου Ανεβάσματος", "Reset Vector Storage/Knowledge": "Επαναφορ<PERSON> Αποθήκευσης Διανυσμάτων/Γνώσης", "Reset view": "", "Response": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "Οι ειδοποιήσεις απάντησης δεν μπορούν να ενεργοποιηθούν καθώς οι άδειες του ιστότοπου έχουν αρνηθεί. Π<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> επισκεφτείτε τις ρυθμίσεις του περιηγητή σας για να δώσετε την απαραίτητη πρόσβαση.", "Response splitting": "Δια<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ς απάντησης", "Response Watermark": "", "Result": "Αποτέλεσμα", "Retrieval": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "Πλούσι<PERSON> Εισαγω<PERSON>ή Κειμένου για Συνομιλία", "RK": "RK", "Role": "Ρόλος", "Rosé Pine": "<PERSON><PERSON><PERSON>", "Rosé Pine Dawn": "<PERSON><PERSON><PERSON>", "RTL": "RTL", "Run": "Εκτέλεση", "Running": "Εκτέλεση", "Save": "Αποθήκευση", "Save & Create": "Αποθήκευση & Δημιουργία", "Save & Update": "Αποθήκευση & Ενημέρωση", "Save As Copy": "Αποθήκευση ως Αντιγραφή", "Save Tag": "Αποθήκευση Ετικέτας", "Saved": "Αποθηκευμένο", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "Η αποθήκευση των αρχείων συνομιλίας απευθείας στη μνήμη αποθήκευσης του προγράμματος περιήγησής σας δεν υποστηρίζεται πλέον. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> αφιερώστε λίγο χρόνο να κατεβάσετε και να διαγράψετε τα αρχεία συνομιλίας σας κάνοντας κλικ στο κουμπί παρακάτω. Μην ανησυχείτε, μπορείτε εύκολα να επαναφέρετε τα αρχεία συνομιλιών σας στο backend μέσω", "Scroll On Branch Change": "", "Search": "Αναζήτηση", "Search a model": "Αναζήτηση μοντέλου", "Search Base": "Βάση Αναζήτησης", "Search Chats": "Αναζήτηση Συνομιλιών", "Search Collection": "Αναζήτη<PERSON>η Συλλογής", "Search Filters": "Φίλτρα Αναζήτησης", "search for tags": "αναζήτηση για ετικέτες", "Search Functions": "Αναζήτη<PERSON>η Λειτουργιών", "Search Knowledge": "Αναζήτηση Γνώσης", "Search Models": "Αναζήτη<PERSON>η Μοντέλων", "Search Notes": "", "Search options": "Επιλ<PERSON><PERSON><PERSON><PERSON> Αναζήτησης", "Search Prompts": "Αναζήτηση Προτροπών", "Search Result Count": "Αριθ<PERSON><PERSON>ς Αποτελεσμάτων Αναζήτησης", "Search the internet": "", "Search Tools": "Αναζήτηση Εργαλείων", "SearchApi API Key": "Κλειδί API SearchApi", "SearchApi Engine": "Μηχανή SearchApi", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "Αναζήτηση \"{{searchQuery}}\"", "Searching Knowledge for \"{{searchQuery}}\"": "Αναζήτηση Γνώσης για \"{{searchQuery}}\"", "Searching the web...": "", "Searxng Query URL": "URL Ερώτησης Searxng", "See readme.md for instructions": "Δείτε readme.md για οδηγίες", "See what's new": "Δείτε τι νέο υπάρχει", "Seed": "Seed", "Select a base model": "Επιλέξτε ένα βασικό μοντέλο", "Select a engine": "Επιλέξτε μια μηχανή", "Select a function": "Επιλέξτε μια λειτουργία", "Select a group": "Επιλέξτε μια ομάδα", "Select a model": "Επιλέξτε ένα μοντέλο", "Select a pipeline": "Επιλέξτε ένα pipeline", "Select a pipeline url": "Επιλέξτε ένα pipeline url", "Select a tool": "Επιλέξτε ένα εργαλείο", "Select an auth method": "", "Select an Ollama instance": "", "Select Engine": "Επιλέξτε Μηχανή", "Select Knowledge": "Επιλέξτε Γνώση", "Select only one model to call": "Επιλέξτε μόνο ένα μοντέλο για κλήση", "Selected model(s) do not support image inputs": "Τα επιλεγμένα μοντέλα δεν υποστηρίζουν είσοδο εικόνων", "Semantic distance to query": "Σημαντικ<PERSON> απόσταση προς την ερώτηση", "Send": "Αποστολή", "Send a Message": "Αποστολή <PERSON>ύ<PERSON>ατος", "Send message": "Αποστολή μηνύματος", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "Στέλνει `stream_options: { include_usage: true }` στο αίτημα.\nΟι υποστηριζόμενοι πάροχοι θα επιστρέψουν πληροφορίες χρήσης token στην απάντηση όταν ρυθμιστεί.", "September": "Σεπτέμβριος", "SerpApi API Key": "", "SerpApi Engine": "", "Serper API Key": "Κλειδί API Serper", "Serply API Key": "Κλειδί API Serply", "Serpstack API Key": "Κλειδί API Serpstack", "Server connection verified": "Η σύνδεση διακομιστή επαληθεύθηκε", "Set as default": "Ορισμός ως προεπιλογή", "Set CFG Scale": "Ορισμός CFG Scale", "Set Default Model": "Ορισμός Προεπιλεγμένου Μοντέλου", "Set embedding model": "Ορισμός μοντέλου ενσωμάτωσης", "Set embedding model (e.g. {{model}})": "Ορισμός μοντέλου ενσωμάτωσης (π.χ. {{model}})", "Set Image Size": "Ορισμός Μεγέθους Εικόνας", "Set reranking model (e.g. {{model}})": "Ορισμός μοντέλου επαναταξινόμησης (π.χ. {{model}})", "Set Sampler": "Ορι<PERSON><PERSON><PERSON><PERSON>", "Set Scheduler": "Ορισμός Scheduler", "Set Steps": "Ορισμ<PERSON>ς Βημάτων", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "Ορισμός του αριθμού των νημάτων εργασίας που χρησιμοποιούνται για υπολογισμούς. Αυτή η επιλογή ελέγχει πόσα νήματα χρησιμοποιούνται για την επεξεργασία των εισερχόμενων αιτημάτων ταυτόχρονα. Η αύξηση αυτής της τιμής μπορεί να βελτιώσει την απόδοση σε εργασίες υψηλής συγχρονισμένης φόρτωσης αλλά μπορεί επίσης να καταναλώσει περισσότερους πόρους CPU.", "Set Voice": "Ορισμ<PERSON>ς <PERSON>ωνής", "Set whisper model": "Ορισμός μοντέλου whisper", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets how far back for the model to look back to prevent repetition.": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "", "Sets the size of the context window used to generate the next token.": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "Ορίζει τις σειρές παύσης που θα χρησιμοποιηθούν. Όταν εντοπιστεί αυτό το μοτίβο, το LLM θα σταματήσει να δημιουργεί κείμενο και θα επιστρέψει. Πολλαπλά μοτίβα παύσης μπορούν να οριστούν καθορίζοντας πολλαπλές ξεχωριστές παραμέτρους παύσης σε ένα αρχείο μοντέλου.", "Settings": "Ρυθμίσεις", "Settings saved successfully!": "Οι Ρυθμίσεις αποθηκεύτηκαν με επιτυχία!", "Share": "Κοινή Χρήσ<PERSON>", "Share Chat": "Κοινή Χρήση Συνομιλίας", "Share to Open WebUI Community": "Κοινή Χρήση στην Κοινότητα OpenWebUI", "Sharing Permissions": "", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "", "Show": "Εμφάνιση", "Show \"What's New\" modal on login": "Εμφάνιση του παράθυρου \"Τι νέο υπάρχει\" κατά την είσοδο", "Show Admin Details in Account Pending Overlay": "Εμφάνιση Λεπτομερειών Διαχειριστή στο Υπέρθεση Εκκρεμής Λογαριασμού", "Show All": "", "Show image preview": "", "Show Less": "", "Show Model": "", "Show shortcuts": "Εμφάνιση συντομεύσεων", "Show your support!": "Δείξτε την υποστήριξή σας!", "Showcased creativity": "Εμφανιζόμενη δημιουργικότητα", "Sign in": "Σύνδεση", "Sign in to {{WEBUI_NAME}}": "Σύνδεση στο {{WEBUI_NAME}}", "Sign in to {{WEBUI_NAME}} with LDAP": "Σύνδεση στο {{WEBUI_NAME}} με LDAP", "Sign Out": "Αποσύνδεση", "Sign up": "Εγγραφή", "Sign up to {{WEBUI_NAME}}": "Εγγραφή στο {{WEBUI_NAME}}", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to True.": "", "Signing in to {{WEBUI_NAME}}": "Σύνδεση στο {{WEBUI_NAME}}", "sk-1234": "", "Skip Cache": "", "Skip the cache and re-run the inference. Defaults to False.": "", "Sougou Search API sID": "", "Sougou Search API SK": "", "Source": "Πηγή", "Speech Playback Speed": "Ταχύτητα Αναπαραγω<PERSON><PERSON>ς Ομιλί<PERSON>ς", "Speech recognition error: {{error}}": "Σφάλμα αναγνώρισης ομιλίας: {{error}}", "Speech-to-Text": "", "Speech-to-Text Engine": "Μηχανή Speech-to-Text", "Stop": "Σταμάτημα", "Stop Generating": "", "Stop Sequence": "Σειρά Παύσης", "Stream Chat Response": "Συνομιλία Ροής Απάντησης", "Strikethrough": "", "Strip Existing OCR": "", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "", "STT Model": "Μοντέλο STT", "STT Settings": "Ρυθμίσεις STT", "Stylized PDF Export": "", "Subtitle (e.g. about the Roman Empire)": "Υπότιτλος (π.χ. για την Ρωμαϊκή Αυτοκρατορία)", "Success": "Επιτυχία", "Successfully updated.": "Επιτ<PERSON><PERSON><PERSON><PERSON> ενημερώθηκε.", "Suggested": "Προτεινόμενο", "Support": "Υποστήριξη", "Support this plugin:": "Υποστήριξη αυτού του plugin:", "Supported MIME Types": "", "Sync directory": "Συγχρο<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> καταλόγου", "System": "Σύστημα", "System Instructions": "Οδηγ<PERSON><PERSON><PERSON>υστήματος", "System Prompt": "Προτρο<PERSON><PERSON>υστήματος", "Tags": "", "Tags Generation": "", "Tags Generation Prompt": "Προτροπή Γενιάς Ετικετών", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "", "Talk to model": "", "Tap to interrupt": "Πατήστε για παύση", "Task List": "", "Task Model": "", "Tasks": "", "Tavily API Key": "Κλειδί API Tavily", "Tavily Extract Depth": "", "Tell us more:": "Πείτε μας περισσότερα:", "Temperature": "Temperature", "Temporary Chat": "Προσωρινή Συνομιλία", "Text Splitter": "Διαχ<PERSON><PERSON>ιστ<PERSON><PERSON>ιμ<PERSON>νου", "Text-to-Speech": "", "Text-to-Speech Engine": "Μηχανή Text-to-Speech", "Thanks for your feedback!": "Ευχαριστούμε για την ανατροφοδότησή σας!", "The Application Account DN you bind with for search": "Το DN του Λογαριασμού Εφαρμογής που συνδέετε για αναζήτηση", "The base to search for users": "Η βάση για αναζήτηση χρηστών", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "Οι προγραμματιστές πίσω από αυτό το plugin είναι παθιασμένοι εθελοντές από την κοινότητα. Αν βρείτε αυτό το plugin χρήσιμο, παρα<PERSON><PERSON><PERSON><PERSON> σκεφτείτε να συνεισφέρετε στην ανάπτυξή του.", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "Η κατάταξη αξιολόγησης βασίζεται στο σύστημα βαθμολόγησης Elo και ενημερώνεται σε πραγματικό χρόνο.", "The format to return a response in. Format can be json or a JSON schema.": "", "The height in pixels to compress images to. Leave empty for no compression.": "", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "", "The LDAP attribute that maps to the mail that users use to sign in.": "", "The LDAP attribute that maps to the username that users use to sign in.": "Το χαρακτηριστικ<PERSON> LDAP που αντιστο<PERSON><PERSON><PERSON><PERSON> στο όνομα χρήστη που χρησιμοποιούν οι χρήστες για να συνδεθούν.", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "Η κατάταξη είναι αυτή τη στιγμή σε beta, και ενδέχεται να προσαρμόσουμε τους υπολογισμούς βαθμολογίας καθώς βελτιώνουμε τον αλγόριθμο.", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "Το μέγιστο μέγεθος αρχείου σε MB. Αν το μέγεθος του αρχείου υπερβαίνει αυτό το όριο, το αρχείο δεν θα ανεβεί.", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "Ο μέγιστος αριθμός αρχείων που μπορούν να χρησιμοποιηθούν ταυτόχρονα στη συνομιλία. Αν ο αριθμός των αρχείων υπερβαίνει αυτό το όριο, τα αρχεία δεν θα ανεβούν.", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "Η βαθμολογία θα πρέπει να είναι μια τιμή μεταξύ 0.0 (0%) και 1.0 (100%).", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "", "The width in pixels to compress images to. Leave empty for no compression.": "", "Theme": "Θέμα", "Thinking...": "Σκέφτομαι...", "This action cannot be undone. Do you wish to continue?": "Αυτή η ενέργεια δεν μπορεί να αναιρεθεί. Θέλετε να συνεχίσετε;", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "", "This chat won't appear in history and your messages will not be saved.": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "Αυτό διασφαλίζει ότι οι πολύτιμες συνομιλίες σας αποθηκεύονται με ασφάλεια στη βάση δεδομένων backend σας. Ευχαριστούμε!", "This feature is experimental and may be modified or discontinued without notice.": "", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "Αυτή είναι μια πειραματική λειτουργία, μπορεί να μην λειτουργεί όπως αναμένεται και υπόκειται σε αλλαγές οποιαδήποτε στιγμή.", "This model is not publicly available. Please select another model.": "", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "Αυτή η επιλογή θα διαγράψει όλα τα υπάρχοντα αρχεία στη συλλογή και θα τα αντικαταστήσει με νέα ανεβασμένα αρχεία.", "This response was generated by \"{{model}}\"": "Αυτή η απάντηση δημιουργήθηκε από \"{{model}}\"", "This will delete": "Αυτό θα διαγράψει", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "Αυτό θα διαγράψει το <strong>{{NAME}}</strong> και <strong>όλο το περιεχόμενό του</strong>.", "This will delete all models including custom models": "Αυτό θα διαγράψει όλα τα μοντέλα, συμπεριλαμβανομένων των προσαρμοσμένων μοντέλων", "This will delete all models including custom models and cannot be undone.": "Αυτό θα διαγράψει όλα τα μοντέλα, συμπεριλαμβανομένων των προσαρμοσμένων μοντέλων και δεν μπορεί να αναιρεθεί.", "This will reset the knowledge base and sync all files. Do you wish to continue?": "Αυτό θα επαναφέρει τη βάση γνώσης και θα συγχρονίσει όλα τα αρχεία. Θέλετε να συνεχίσετε;", "Thorough explanation": "Λεπτομε<PERSON>ής εξήγηση", "Thought for {{DURATION}}": "", "Thought for {{DURATION}} seconds": "", "Tika": "<PERSON><PERSON>", "Tika Server URL required.": "Απαιτείται το URL διακομιστή Tika.", "Tiktoken": "Tiktoken", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "Συμβουλή: Ενημερώστε πολλαπλές θέσεις μεταβλητών διαδοχικά πατώντας το πλήκτρο tab στο πεδίο συνομιλίας μετά από κάθε αντικατάσταση.", "Title": "Τίτλος", "Title (e.g. Tell me a fun fact)": "<PERSON><PERSON><PERSON><PERSON><PERSON> (π.χ. Πες μου ένα διασκεδαστικ<PERSON> γεγονός)", "Title Auto-Generation": "Αυτόματη Γενιά Τίτλων", "Title cannot be an empty string.": "Ο τίτλος δεν μπορεί να είναι κενή συμβολοσειρά.", "Title Generation": "", "Title Generation Prompt": "Προτροπή Δημιουρ<PERSON><PERSON><PERSON>ς Τίτλου", "TLS": "TLS", "To access the available model names for downloading,": "Για να αποκτήσετε πρόσβαση στα διαθέσιμα ονόματα μοντέλων για λήψη,", "To access the GGUF models available for downloading,": "Για να αποκτήσετε πρόσβαση στα μοντέλα GGUF διαθέσιμα για λήψη,", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "Για να αποκτήσετε πρόσβαση στο <PERSON>, παρα<PERSON><PERSON><PERSON><PERSON> επικοινωνήστε με τον διαχειριστή. Οι διαχειριστές μπορούν να διαχειριστούν τις καταστάσεις των χρηστών από τον Πίνακα Διαχειριστή.", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "Για να επισυνάψετε τη βάση γνώσης εδώ, προσθέστε τα πρώτα στο χώρο εργασίας \"Knowledge\".", "To learn more about available endpoints, visit our documentation.": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "Για να προστατεύσετε την ιδιωτικ<PERSON><PERSON>ητ<PERSON> σας, μόν<PERSON> <PERSON>ι βαθμολογίες, τα <PERSON>s μοντέλων, οι ετικέτες και τα μεταδεδομένα μοιράζονται από την ανατροφοδότησή σας—τα αρχεία συνομιλιών σας παραμένουν ιδιωτικά και δεν περιλαμβάνονται.", "To select actions here, add them to the \"Functions\" workspace first.": "Για να επιλέξετε ενέργειες εδώ, προσθέστε τις πρώτα στο χώρο εργασίας \"Functions\".", "To select filters here, add them to the \"Functions\" workspace first.": "Για να επιλέξετε φίλτρα εδώ, προσθέστε τα πρώτα στο χώρο εργασίας \"Functions\".", "To select toolkits here, add them to the \"Tools\" workspace first.": "Για να επιλέξετε toolkits εδώ, προσθέστε τα πρώτα στο χώρο εργασίας \"Tools\".", "Toast notifications for new updates": "Ειδοποιήσεις Toast για νέες ενημερώσεις", "Today": "Σήμερα", "Toggle search": "", "Toggle settings": "Εναλλαγή ρυθμίσεων", "Toggle sidebar": "Εναλλαγή πλαϊνού μενού", "Toggle whether current connection is active.": "", "Token": "Token", "Too verbose": "Πολ<PERSON> λεπτομερές", "Tool created successfully": "Το εργαλείο δημιουργήθηκε με επιτυχία", "Tool deleted successfully": "Το εργαλείο διαγράφηκε με επιτυχία", "Tool Description": "Περιγραφή Εργαλείου", "Tool ID": "ID Εργαλείου", "Tool imported successfully": "Το εργαλείο εισήχθη με επιτυχία", "Tool Name": "Όνομα Εργαλείου", "Tool Servers": "", "Tool updated successfully": "Το εργαλείο ενημερώθηκε με επιτυχία", "Tools": "Εργαλεία", "Tools Access": "Πρόσβαση Εργαλείων", "Tools are a function calling system with arbitrary code execution": "Τα εργαλεία είναι ένα σύστημα κλήσης λειτουργιών με αυθαίρετη εκτέλεση κώδικα", "Tools Function Calling Prompt": "", "Tools have a function calling system that allows arbitrary code execution.": "Τα εργαλεία διαθέτουν ένα σύστημα κλήσης λειτουργιών που επιτρέπει την αυθαίρετη εκτέλεση κώδικα.", "Tools Public Sharing": "", "Top K": "Top K", "Top K Reranker": "", "Transformers": "Transformers", "Trouble accessing Ollama?": "Προβλήματα πρόσβασης στο <PERSON>?", "Trust Proxy Environment": "", "TTS Model": "Μοντ<PERSON><PERSON><PERSON> TTS", "TTS Settings": "Ρυθμίσεις TTS", "TTS Voice": "Φωνή TTS", "Type": "Τύπος", "Type Hugging Face Resolve (Download) URL": "<PERSON><PERSON><PERSON><PERSON>RL Ανάλυσης Hugging <PERSON> Resolve (Λήψη)", "Uh-oh! There was an issue with the response.": "", "UI": "Διεπαφ<PERSON> Χρήστη (UI)", "Unarchive All": "Απο-αρχειοθέτηση Όλων", "Unarchive All Archived Chats": "Απο-αρχειοθέτηση Όλων των Αρχειοθετημένων Συνομιλιών", "Unarchive Chat": "Απο-αρχειοθέτηση Συνομιλίας", "Underline": "", "Unloads {{FROM_NOW}}": "", "Unlock mysteries": "Ξεκλείδωμα μυστηρίων", "Unpin": "Αφαίρεση καρφίτσματος", "Unravel secrets": "Ξετυλίξτε μυστικά", "Untagged": "<PERSON><PERSON><PERSON><PERSON><PERSON>ς", "Untitled": "", "Update": "Ενημέρωση", "Update and Copy Link": "Ενημέρωση και Αντιγραφή Συνδέσμου", "Update for the latest features and improvements.": "Ενημέρωση για τις τελευταίες λειτουργίες και βελτιώσεις.", "Update password": "Ενημέρωση κωδικού", "Updated": "Ενημερώθηκε", "Updated at": "Ενημερώθηκε στις", "Updated At": "Ενημερώθηκε στις", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "", "Upload": "Ανέβασμα", "Upload a GGUF model": "Ανέβασμα μοντέλου GGUF", "Upload Audio": "", "Upload directory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> αν<PERSON>β<PERSON><PERSON>ματος", "Upload files": "Ανέβασμα αρχείων", "Upload Files": "Ανέβασμα Αρχείων", "Upload Pipeline": "Ανέβασμα Pipeline", "Upload Progress": "Πρ<PERSON><PERSON><PERSON><PERSON> Ανεβάσματος", "URL": "URL", "URL Mode": "Λειτουργία URL", "Usage": "", "Use '#' in the prompt input to load and include your knowledge.": "Χρησιμοποιήστε '#' στην είσοδο προτροπής για φόρτωση και συμπερίληψη της γνώσης σας.", "Use Gravatar": "Χρησιμοποιήστε Gravatar", "Use groups to group your users and assign permissions.": "Χρησιμοποιήστε ομάδες για να ομαδοποιήσετε τους χρήστες σας και να αναθέσετε δικαιώματα.", "Use Initials": "Χρησιμοποιήστε Αρχικά", "Use LLM": "", "Use no proxy to fetch page contents.": "", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "", "user": "user", "User": "<PERSON>ρή<PERSON><PERSON><PERSON>ς", "User location successfully retrieved.": "Η τοποθεσία του χρήστη ανακτήθηκε με επιτυχία.", "User menu": "", "User Webhooks": "", "Username": "Όνομα Χρήστη", "Users": "<PERSON>ρ<PERSON><PERSON><PERSON><PERSON><PERSON>", "Using the default arena model with all models. Click the plus button to add custom models.": "Χρησιμοποιώντας το προεπιλεγμένο μοντέλο arena με όλα τα μοντέλα. Κάντε κλικ στο κουμπί συν για να προσθέσετε προσαρμοσμένα μοντέλα.", "Utilize": "Αξιοποίηση", "Valid time units:": "Έγκυρες μονάδες χρόνου:", "Valves": "Βαλβίδες", "Valves updated": "Οι βαλβίδες ενημερώθηκαν", "Valves updated successfully": "Οι βαλβίδες ενημερώθηκαν με επιτυχία", "variable": "μεταβλητή", "variable to have them replaced with clipboard content.": "μεταβλητή να αντικατασταθούν με το περιεχόμενο του πρόχειρου.", "Verify Connection": "", "Verify SSL Certificate": "", "Version": "Έκδοση", "Version {{selectedVersion}} of {{totalVersions}}": "Έκδοση {{selectedVersion}} από {{totalVersions}}", "View Replies": "", "View Result from **{{NAME}}**": "", "Visibility": "Ορατότητα", "Vision": "", "Voice": "Φωνή", "Voice Input": "Εισαγωγ<PERSON> Φωνής", "Voice mode": "", "Warning": "Προειδοποίηση", "Warning:": "Προειδοποίηση:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "Προειδοποίηση: Η ενεργοποίηση αυτού θα επιτρέψει στους χρήστες να ανεβάσουν αυθαίρετο κώδικα στον διακομιστή.", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "Προειδοποίηση: Αν ενημερώσετε ή αλλάξετε το μοντέλο ενσωμάτωσής σας, θα χρειαστεί να επαναφέρετε όλα τα έγγραφα.", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "", "Web": "Διαδίκτυο", "Web API": "Web API", "Web Loader Engine": "", "Web Search": "Αναζήτηση στο Διαδίκτυο", "Web Search Engine": "Μηχανή Αναζήτησης στο Διαδίκτυο", "Web Search in Chat": "", "Web Search Query Generation": "", "Webhook URL": "URL Webhook", "WebUI Settings": "Ρυθμίσεις WebUI", "WebUI URL": "", "WebUI will make requests to \"{{url}}\"": "", "WebUI will make requests to \"{{url}}/api/chat\"": "Το WebUI θα κάνει αιτήματα στο \"{{url}}/api/chat\"", "WebUI will make requests to \"{{url}}/chat/completions\"": "Το WebUI θα κάνει αιτήματα στο \"{{url}}/chat/completions\"", "Weight of BM25 Retrieval": "", "What are you trying to achieve?": "Τι προσπαθείτε να πετύχετε?", "What are you working on?": "Τι εργάζεστε;", "What's New in": "Τι νέο υπάρχει στο", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "Όταν ενεργοπ<PERSON><PERSON>ηθε<PERSON>, το μοντέλο θα ανταποκρίνεται σε κάθε μήνυμα συνομιλίας σε πραγματικ<PERSON> χρόνο, δημιουργώντας μια απάντηση μόλις ο χρήστης στείλει ένα μήνυμα. Αυτή η λειτουργία είναι χρήσιμη για εφαρμογές ζωντανής συνομιλίας, αλλά μπορεί να επηρεάσει την απόδοση σε πιο αργό υλικό.", "wherever you are": "οπουδήποτε βρίσκεστε", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "", "Whisper (Local)": "<PERSON><PERSON><PERSON> (Τοπικό)", "Why?": "Γιατί?", "Widescreen Mode": "Λειτουρ<PERSON><PERSON><PERSON> Οθόνης Ευρείας", "Won": "Κέρδισε", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "", "Workspace": "<PERSON><PERSON><PERSON><PERSON>", "Workspace Permissions": "Δικ<PERSON><PERSON>ώ<PERSON><PERSON><PERSON><PERSON> Χώρου Εργασίας", "Write": "", "Write a prompt suggestion (e.g. Who are you?)": "Γράψτε μια πρόταση προτροπής (π.χ. Ποιος είσαι;)", "Write a summary in 50 words that summarizes [topic or keyword].": "Γράψτε μια περίληψη σε 50 λέξεις που συνοψίζει [θέμα ή λέξη-κλειδί].", "Write something...": "Γράψτε κάτι...", "Yacy Instance URL": "", "Yacy Password": "", "Yacy Username": "", "Yesterday": "Εχθές", "You": "Εσ<PERSON><PERSON>ς", "You are currently using a trial license. Please contact support to upgrade your license.": "", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "Μπορείτε να συνομιλήσετε μόνο με μέγιστο αριθμό {{maxCount}} αρχείου(-ων) ταυτόχρονα.", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "Μπορείτε να προσωποποιήσετε τις αλληλεπιδράσεις σας με τα LLMs προσθέτοντας αναμνήσεις μέσω του κουμπιού 'Διαχείριση' παρακάτω, κάνοντάς τα πιο χρήσιμα και προσαρμοσμένα σε εσάς.", "You cannot upload an empty file.": "Δεν μπορείτε να ανεβάσετε ένα κενό αρχείο.", "You do not have permission to upload files.": "Δεν έχετε άδεια να ανεβάσετε αρχεία.", "You have no archived conversations.": "Δεν έχετε αρχειοθετημένες συνομιλίες.", "You have shared this chat": "Έχετε μοιραστεί αυτή τη συνομιλία", "You're a helpful assistant.": "Είστε ένας βοηθητικός βοηθός.", "You're now logged in.": "Τώρα είστε συνδεδεμένοι.", "Your account status is currently pending activation.": "Η κατάσταση του λογαριασμού σας είναι αυτή τη στιγμή σε εκκρεμότητα ενεργοποίησης.", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "Η ολόκληρη η συνεισφορά σας θα πάει απευθείας στον προγραμματιστή του plugin· το Open WebUI δεν παίρνει κανένα ποσοστό. Ωστόσ<PERSON>, η επιλεγμένη πλατφόρμα χρηματοδότησης μπορεί να έχει τα δικά της τέλη.", "Youtube": "Youtube", "Youtube Language": "", "Youtube Proxy URL": ""}
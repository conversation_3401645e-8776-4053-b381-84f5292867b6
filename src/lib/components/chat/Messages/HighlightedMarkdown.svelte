<script>
	import { onMount, onDestroy, afterUpdate } from 'svelte';
	import { highlightedSegments } from '$lib/stores';
	import { getHighlightsForMessage, removeHighlight } from '$lib/utils/highlights';
	import Markdown from './Markdown.svelte';

	export let id = '';
	export let content;
	export let model = null;
	export let save = false;
	export let preview = false;
	export let sourceIds = [];
	export let onSave = () => {};
	export let onUpdate = () => {};
	export let onPreview = () => {};
	export let onSourceClick = () => {};
	export let onTaskClick = () => {};

	let containerElement;
	let originalTextContent = '';

	// Apply highlights to the rendered content
	const applyHighlights = () => {
		if (!containerElement || !id) return;

		const highlights = getHighlightsForMessage(id);
		if (highlights.length === 0) {
			// Remove any existing highlights
			const existingHighlights = containerElement.querySelectorAll('.highlighted-text');
			existingHighlights.forEach(el => {
				const parent = el.parentNode;
				parent.replaceChild(document.createTextNode(el.textContent), el);
				parent.normalize();
			});
			return;
		}

		// Get all text nodes in the container
		const walker = document.createTreeWalker(
			containerElement,
			NodeFilter.SHOW_TEXT,
			null,
			false
		);

		const textNodes = [];
		let node;
		while (node = walker.nextNode()) {
			textNodes.push(node);
		}

		// Build a map of text positions to nodes
		let currentOffset = 0;
		const positionMap = [];
		
		textNodes.forEach(textNode => {
			const text = textNode.textContent;
			positionMap.push({
				node: textNode,
				startOffset: currentOffset,
				endOffset: currentOffset + text.length,
				text: text
			});
			currentOffset += text.length;
		});

		// Apply highlights in reverse order to avoid offset issues
		const sortedHighlights = highlights.sort((a, b) => b.startOffset - a.startOffset);

		sortedHighlights.forEach(highlight => {
			// Find the text nodes that contain this highlight
			const affectedNodes = positionMap.filter(nodeInfo => 
				nodeInfo.startOffset < highlight.endOffset && 
				nodeInfo.endOffset > highlight.startOffset
			);

			if (affectedNodes.length === 0) return;

			// For simplicity, handle single-node highlights first
			if (affectedNodes.length === 1) {
				const nodeInfo = affectedNodes[0];
				const node = nodeInfo.node;
				const nodeStartInHighlight = Math.max(0, highlight.startOffset - nodeInfo.startOffset);
				const nodeEndInHighlight = Math.min(nodeInfo.text.length, highlight.endOffset - nodeInfo.startOffset);

				if (nodeStartInHighlight < nodeEndInHighlight) {
					const beforeText = nodeInfo.text.substring(0, nodeStartInHighlight);
					const highlightText = nodeInfo.text.substring(nodeStartInHighlight, nodeEndInHighlight);
					const afterText = nodeInfo.text.substring(nodeEndInHighlight);

					// Create highlight span
					const highlightSpan = document.createElement('span');
					highlightSpan.className = 'highlighted-text';
					highlightSpan.setAttribute('data-highlight-id', highlight.id);
					highlightSpan.setAttribute('title', 'Ctrl+click to remove highlight');
					highlightSpan.textContent = highlightText;

					// Create remove button
					const removeBtn = document.createElement('button');
					removeBtn.className = 'highlight-remove-btn';
					removeBtn.textContent = '×';
					removeBtn.title = 'Remove highlight';
					removeBtn.onclick = (e) => {
						e.preventDefault();
						e.stopPropagation();
						removeHighlight(highlight.id);
						applyHighlights(); // Re-apply highlights
					};
					highlightSpan.appendChild(removeBtn);

					// Add Ctrl+click handler to the span
					highlightSpan.onclick = (e) => {
						if (e.ctrlKey) {
							e.preventDefault();
							e.stopPropagation();
							removeHighlight(highlight.id);
							applyHighlights(); // Re-apply highlights
						}
					};

					// Replace the text node
					const parent = node.parentNode;
					if (beforeText) {
						parent.insertBefore(document.createTextNode(beforeText), node);
					}
					parent.insertBefore(highlightSpan, node);
					if (afterText) {
						parent.insertBefore(document.createTextNode(afterText), node);
					}
					parent.removeChild(node);
				}
			}
		});
	};

	// Listen for highlight changes
	const handleHighlightsChanged = () => {
		applyHighlights();
	};

	onMount(() => {
		window.addEventListener('highlightsChanged', handleHighlightsChanged);
	});

	onDestroy(() => {
		window.removeEventListener('highlightsChanged', handleHighlightsChanged);
	});

	afterUpdate(() => {
		// Apply highlights after the content is rendered
		setTimeout(applyHighlights, 0);
	});

	// Update when highlights store changes
	$: if ($highlightedSegments) {
		setTimeout(applyHighlights, 0);
	}
</script>

<div bind:this={containerElement}>
	<Markdown
		{id}
		{content}
		{model}
		{save}
		{preview}
		{sourceIds}
		{onSave}
		{onUpdate}
		{onPreview}
		{onSourceClick}
		{onTaskClick}
	/>
</div>

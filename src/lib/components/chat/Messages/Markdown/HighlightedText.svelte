<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { highlightedSegments } from '$lib/stores';
	import { applyHighlightsToText, removeHighlight } from '$lib/utils/highlights';

	export let text: string;
	export let messageId: string;
	export let textOffset: number = 0;

	let highlightedHtml: string = '';
	let containerElement: HTMLElement;

	// Function to update the highlighted HTML
	const updateHighlights = () => {
		console.log('Updating highlights for:', { messageId, text: text.substring(0, 50) + '...', textOffset });
		highlightedHtml = applyHighlightsToText(text, messageId, textOffset);
		console.log('Highlighted HTML:', highlightedHtml !== text ? 'Has highlights' : 'No highlights');
	};

	// Handle highlight removal clicks
	const handleHighlightClick = (event: Event) => {
		const target = event.target as HTMLElement;
		const mouseEvent = event as MouseEvent;

		if (target.classList.contains('highlight-remove-btn')) {
			event.preventDefault();
			event.stopPropagation();

			const highlightId = target.parentElement?.getAttribute('data-highlight-id');
			if (highlightId) {
				removeHighlight(highlightId);
				updateHighlights();
			}
		}
		// Also handle clicks on the highlighted text itself for removal
		else if (target.classList.contains('highlighted-text')) {
			const highlightId = target.getAttribute('data-highlight-id');
			if (highlightId && mouseEvent.ctrlKey) { // Require Ctrl+click to avoid accidental removal
				event.preventDefault();
				event.stopPropagation();
				removeHighlight(highlightId);
				updateHighlights();
			}
		}
	};

	// Listen for highlight changes
	const handleHighlightsChanged = () => {
		updateHighlights();
	};

	onMount(() => {
		updateHighlights();

		// Listen for global highlight changes
		window.addEventListener('highlightsChanged', handleHighlightsChanged);

		// Add click handler for highlight removal
		if (containerElement) {
			containerElement.addEventListener('click', handleHighlightClick);
		}
	});

	onDestroy(() => {
		window.removeEventListener('highlightsChanged', handleHighlightsChanged);
		if (containerElement) {
			containerElement.removeEventListener('click', handleHighlightClick);
		}
	});

	// Update when highlights store changes
	$: if ($highlightedSegments) {
		updateHighlights();
	}
</script>

<span bind:this={containerElement}>
	{#if highlightedHtml && highlightedHtml !== text}
		{@html highlightedHtml}
	{:else}
		{text}
	{/if}
</span>

<script lang="ts">
	import { onMount, getContext } from 'svelte';
	import { highlightedSegments } from '$lib/stores';
	import { 
		getAllHighlightedText, 
		copyAllHighlights, 
		clearAllHighlights, 
		removeHighlight,
		clearHighlightsForChat 
	} from '$lib/utils/highlights';
	import { toast } from 'svelte-sonner';

	const i18n = getContext('i18n');

	export let show = false;
	export let chatId = '';

	let searchQuery = '';
	let filteredHighlights = [];

	// Filter highlights based on search query and current chat
	$: {
		let highlights = $highlightedSegments;
		
		// Filter by current chat if chatId is provided
		if (chatId) {
			highlights = highlights.filter(h => h.chatId === chatId);
		}
		
		// Filter by search query
		if (searchQuery.trim()) {
			highlights = highlights.filter(h => 
				h.text.toLowerCase().includes(searchQuery.toLowerCase())
			);
		}
		
		// Sort by timestamp (newest first)
		filteredHighlights = highlights.sort((a, b) => b.timestamp - a.timestamp);
	}

	const handleCopyAll = async () => {
		const success = await copyAllHighlights();
		if (success) {
			toast.success('All highlights copied to clipboard');
		} else {
			toast.error('No highlights to copy');
		}
	};

	const handleClearAll = () => {
		if (confirm('Are you sure you want to clear all highlights? This action cannot be undone.')) {
			clearAllHighlights();
			toast.success('All highlights cleared');
		}
	};

	const handleClearChat = () => {
		if (chatId && confirm('Are you sure you want to clear highlights for this chat?')) {
			clearHighlightsForChat(chatId);
			toast.success('Chat highlights cleared');
		}
	};

	const handleRemoveHighlight = (highlightId: string) => {
		removeHighlight(highlightId);
		toast.success('Highlight removed');
	};

	const formatTimestamp = (timestamp: number) => {
		return new Date(timestamp).toLocaleString();
	};

	const truncateText = (text: string, maxLength: number = 100) => {
		return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
	};
</script>

{#if show}
	<div class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
		<div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[80vh] overflow-hidden">
			<!-- Header -->
			<div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
				<h2 class="text-xl font-semibold text-gray-900 dark:text-white">
					Highlights {chatId ? '(Current Chat)' : '(All Chats)'}
				</h2>
				<button
					on:click={() => show = false}
					class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
				>
					<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
					</svg>
				</button>
			</div>

			<!-- Search and Actions -->
			<div class="p-4 border-b border-gray-200 dark:border-gray-700">
				<div class="flex flex-col sm:flex-row gap-4">
					<div class="flex-1">
						<input
							type="text"
							placeholder="Search highlights..."
							bind:value={searchQuery}
							class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
						/>
					</div>
					<div class="flex gap-2">
						<button
							on:click={handleCopyAll}
							class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
						>
							Copy All
						</button>
						{#if chatId}
							<button
								on:click={handleClearChat}
								class="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors"
							>
								Clear Chat
							</button>
						{/if}
						<button
							on:click={handleClearAll}
							class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
						>
							Clear All
						</button>
					</div>
				</div>
			</div>

			<!-- Highlights List -->
			<div class="flex-1 overflow-y-auto p-4">
				{#if filteredHighlights.length === 0}
					<div class="text-center py-8 text-gray-500 dark:text-gray-400">
						{#if searchQuery.trim()}
							No highlights found matching "{searchQuery}"
						{:else}
							No highlights yet. Select text in AI responses and click "Highlight" to get started.
						{/if}
					</div>
				{:else}
					<div class="space-y-3">
						{#each filteredHighlights as highlight (highlight.id)}
							<div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
								<div class="flex items-start justify-between">
									<div class="flex-1">
										<p class="text-gray-900 dark:text-white mb-2">
											{highlight.text}
										</p>
										<div class="text-sm text-gray-500 dark:text-gray-400">
											<span>Message: {highlight.messageId.substring(0, 8)}...</span>
											<span class="mx-2">•</span>
											<span>{formatTimestamp(highlight.timestamp)}</span>
										</div>
									</div>
									<button
										on:click={() => handleRemoveHighlight(highlight.id)}
										class="ml-4 text-red-500 hover:text-red-700 transition-colors"
										title="Remove highlight"
									>
										<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
										</svg>
									</button>
								</div>
							</div>
						{/each}
					</div>
				{/if}
			</div>

			<!-- Footer -->
			<div class="p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
				<div class="text-sm text-gray-600 dark:text-gray-400">
					Total highlights: {filteredHighlights.length}
					{#if chatId}
						in this chat
					{:else}
						across all chats
					{/if}
				</div>
			</div>
		</div>
	</div>
{/if}

import { get } from 'svelte/store';
import { highlightedSegments, type HighlightedSegment } from '$lib/stores';
import { v4 as uuidv4 } from 'uuid';

/**
 * Add a new highlighted text segment
 */
export function addHighlight(
	text: string,
	messageId: string,
	chatId: string,
	startOffset: number,
	endOffset: number
): string {
	const id = uuidv4();
	const highlight: HighlightedSegment = {
		id,
		text,
		messageId,
		chatId,
		timestamp: Date.now(),
		startOffset,
		endOffset
	};

	highlightedSegments.update((segments) => [...segments, highlight]);

	// Store in localStorage for persistence
	localStorage.setItem('highlightedSegments', JSON.stringify(get(highlightedSegments)));

	return id;
}

/**
 * Remove a highlighted text segment by ID
 */
export function removeHighlight(highlightId: string): void {
	highlightedSegments.update((segments) =>
		segments.filter((segment) => segment.id !== highlightId)
	);

	// Update localStorage
	localStorage.setItem('highlightedSegments', JSON.stringify(get(highlightedSegments)));
}

/**
 * Get all highlighted segments for a specific message
 */
export function getHighlightsForMessage(messageId: string): HighlightedSegment[] {
	return get(highlightedSegments).filter((segment) => segment.messageId === messageId);
}

/**
 * Get all highlighted segments for a specific chat
 */
export function getHighlightsForChat(chatId: string): HighlightedSegment[] {
	return get(highlightedSegments).filter((segment) => segment.chatId === chatId);
}

/**
 * Clear all highlighted segments
 */
export function clearAllHighlights(): void {
	highlightedSegments.set([]);
	localStorage.removeItem('highlightedSegments');
}

/**
 * Clear highlights for a specific chat
 */
export function clearHighlightsForChat(chatId: string): void {
	highlightedSegments.update((segments) =>
		segments.filter((segment) => segment.chatId !== chatId)
	);

	// Update localStorage
	localStorage.setItem('highlightedSegments', JSON.stringify(get(highlightedSegments)));
}

/**
 * Load highlighted segments from localStorage
 */
export function loadHighlightsFromStorage(): void {
	try {
		const stored = localStorage.getItem('highlightedSegments');
		if (stored) {
			const segments: HighlightedSegment[] = JSON.parse(stored);
			highlightedSegments.set(segments);
		}
	} catch (error) {
		console.error('Failed to load highlights from storage:', error);
		highlightedSegments.set([]);
	}
}

/**
 * Get all highlighted text as a single string
 */
export function getAllHighlightedText(): string {
	const segments = get(highlightedSegments);
	return segments
		.sort((a, b) => a.timestamp - b.timestamp)
		.map((segment) => segment.text)
		.join('\n\n');
}

/**
 * Copy all highlighted text to clipboard
 */
export async function copyAllHighlights(): Promise<boolean> {
	try {
		const text = getAllHighlightedText();
		if (text.trim()) {
			await navigator.clipboard.writeText(text);
			return true;
		}
		return false;
	} catch (error) {
		console.error('Failed to copy highlights to clipboard:', error);
		return false;
	}
}

/**
 * Check if a text range is already highlighted
 */
export function isRangeHighlighted(
	messageId: string,
	startOffset: number,
	endOffset: number
): HighlightedSegment | null {
	const segments = getHighlightsForMessage(messageId);
	return segments.find((segment) =>
		segment.startOffset === startOffset && segment.endOffset === endOffset
	) || null;
}

/**
 * Apply visual highlighting to text content
 * This function wraps highlighted text segments with HTML spans
 */
export function applyHighlightsToText(
	text: string,
	messageId: string,
	textOffset: number = 0
): string {
	const highlights = getHighlightsForMessage(messageId);
	if (highlights.length === 0) {
		return text;
	}

	// Sort highlights by start position to process them in order
	const sortedHighlights = highlights
		.filter(highlight => {
			// Only include highlights that overlap with this text segment
			const highlightStart = highlight.startOffset - textOffset;
			const highlightEnd = highlight.endOffset - textOffset;
			return highlightEnd > 0 && highlightStart < text.length;
		})
		.sort((a, b) => a.startOffset - b.startOffset);

	if (sortedHighlights.length === 0) {
		return text;
	}

	let result = '';
	let lastIndex = 0;

	for (const highlight of sortedHighlights) {
		const start = Math.max(0, highlight.startOffset - textOffset);
		const end = Math.min(text.length, highlight.endOffset - textOffset);

		// Add text before the highlight
		if (start > lastIndex) {
			result += text.slice(lastIndex, start);
		}

		// Add the highlighted text
		if (end > start) {
			const highlightedText = text.slice(start, end);
			result += `<span class="highlighted-text" data-highlight-id="${highlight.id}" title="Ctrl+click to remove highlight">${highlightedText}<button class="highlight-remove-btn" title="Remove highlight">×</button></span>`;
		}

		lastIndex = end;
	}

	// Add remaining text
	if (lastIndex < text.length) {
		result += text.slice(lastIndex);
	}

	return result;
}

/**
 * Initialize global highlight removal function
 * This makes the removeHighlight function available globally for onclick handlers
 */
export function initializeGlobalHighlightHandlers(): void {
	if (typeof window !== 'undefined') {
		(window as any).removeHighlight = (highlightId: string) => {
			removeHighlight(highlightId);
			// Trigger a re-render by dispatching a custom event
			window.dispatchEvent(new CustomEvent('highlightsChanged', { detail: { highlightId } }));
		};
	}
}
